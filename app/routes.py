from flask import Blueprint, render_template, request, jsonify, current_app, redirect, url_for, make_response, session, flash
from app.news_service import NewsService
from app.summarizer import NewsSummarizer
from app.youtube_service import YouTubeService
from app.models import Article, User, Admin, db
from datetime import datetime, timedelta
import threading
import time
import json
import os
from openai import OpenAI
import hashlib
import re
from flask_caching import Cache
import requests
from urllib.parse import urlparse

from . import cache
from app import db, cache, socketio

main = Blueprint('main', __name__)
news_service = NewsService()
youtube_service = YouTubeService()
summarizer = NewsSummarizer()

# Add thread lock for background context generation
# This prevents race conditions when multiple requests come in for the same article
background_cache_lock = threading.RLock()

# Helper function to check if user is authenticated
def is_authenticated():
    return 'user_id' in session

# Helper function to check if user is admin
def is_admin():
    return is_authenticated() and session.get('is_admin', False)

# Add middleware to check authentication for protected routes
@main.before_request
def check_auth():
    # Skip authentication checks for all routes
    # Only check admin routes for admin access
    if request.endpoint and request.endpoint.startswith('main.admin_') and not is_admin():
        flash('You must be an admin to access this page.', 'danger')
        return redirect(url_for('main.admin_login'))

    # No other authentication checks - users can access all non-admin routes without verification


# Helper functions for date handling
def format_date(date_str):
    """Format a date string for display in the UI."""
    if not date_str:
        return ""

    try:
        # Try to parse date in NewsAPI format (2023-09-26T12:45:00Z)
        date_obj = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%SZ')
    except ValueError:
        try:
            # Try to parse date in NewsData.io format (2023-09-26 12:45:00)
            date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            # If neither format works, try a more flexible approach
            try:
                # Use dateutil parser which can handle various formats
                from dateutil import parser
                date_obj = parser.parse(date_str)
            except:
                # If all parsing attempts fail, return original string
                return date_str

    # Get current time in UTC
    now = datetime.utcnow()

    # Calculate the difference in time
    delta = now - date_obj

    # Format based on how recent the article is
    if delta.total_seconds() < 3600:  # Less than 1 hour
        minutes = int(delta.total_seconds() / 60)
        return f"{minutes} min ago"
    elif delta.total_seconds() < 86400:  # Less than 1 day
        hours = int(delta.total_seconds() / 3600)
        return f"{hours} hr ago"
    elif delta.days < 7:  # Less than 1 week
        return f"{delta.days} days ago"
    else:
        # Format as "Month Day, Year"
        return date_obj.strftime("%b %d, %Y")

def is_within_days(date_str, days=3):
    """Check if a date string is within the specified number of days from now."""
    if not date_str:
        return False

    # Get current time in UTC
    now = datetime.utcnow()

    try:
        # Try to parse date in NewsAPI format (2023-09-26T12:45:00Z)
        article_date = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%SZ')
    except ValueError:
        try:
            # Try to parse date in NewsData.io format (2023-09-26 12:45:00)
            article_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            # If neither format works, try a more flexible approach
            try:
                # Use dateutil parser which can handle various formats
                from dateutil import parser
                article_date = parser.parse(date_str)
            except:
                # If all parsing attempts fail, return False
                return False

    # Calculate the difference in days
    delta = now - article_date

    # Check if within the specified number of days
    return delta.days <= days

def is_recent_article(date_str, hours=12):
    """Check if an article was published within the last X hours."""
    if not date_str:
        return False

    # Get current time in UTC
    now = datetime.utcnow()

    try:
        # Try to parse date in NewsAPI format (2023-09-26T12:45:00Z)
        article_date = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%SZ')
    except ValueError:
        try:
            # Try to parse date in NewsData.io format (2023-09-26 12:45:00)
            article_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            # If neither format works, try a more flexible approach
            try:
                # Use dateutil parser which can handle various formats
                from dateutil import parser
                article_date = parser.parse(date_str)
            except:
                # If all parsing attempts fail, return False
                return False

    # Calculate the difference in hours
    delta = now - article_date
    delta_hours = delta.total_seconds() / 3600

    # Check if within the specified number of hours
    return delta_hours <= hours

# Setup OpenAI client
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
llm_client = OpenAI(api_key=OPENAI_API_KEY)

# Define the list of allowed topics centrally
# Separate news topics from newsletters for better UX
NEWS_TOPICS = ['Technology', 'Science', 'Business', 'Politics', 'World', 'Sports', 'Entertainment', 'Health']
NEWSLETTER_TOPICS = ['Technology', 'Business', 'Science', 'Design', 'Marketing', 'AI & Machine Learning', 'Startups', 'Finance']
YOUTUBE_CHANNELS = ['ByteByteGo']

# Keep the combined list for validation purposes - remove duplicates
ALLOWED_TOPICS = list(set(NEWS_TOPICS + NEWSLETTER_TOPICS + ['YouTube Channels']))

# Note: Now using OpenAI's built-in web search instead of Brave Search
# This eliminates the need for a separate search API key

# Enhanced cache for background content
background_cache = {}

# Cache for article content to avoid duplicate extraction
article_content_cache = {}

# Maximum cache size and time-to-live (in seconds)
CACHE_MAX_SIZE = 500
CACHE_TTL = 3600 * 24 * 7  # 7 days

# Function to generate article content cache key
def generate_article_content_key(url):
    """Generate a standardized cache key for article content"""
    if not url:
        return None
    # Remove query parameters to avoid cache misses from cache busters
    parsed_url = urlparse(url)
    clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
    return f"article_content_{clean_url}"

def generate_cache_key(prefix, *args):
    """Generate a unique key for caching based on a prefix and multiple arguments"""
    # Process URL arguments to remove cache busters and query parameters
    processed_args = []
    for arg in args:
        if isinstance(arg, str) and arg.startswith('http'):
            # For URLs, remove query parameters to avoid cache misses from cache busters
            parsed_url = urlparse(arg)
            clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
            processed_args.append(clean_url)
        else:
            processed_args.append(arg)

    # Create a key by joining the prefix and processed args
    key = prefix + "_" + "_".join(str(a) for a in processed_args if a)
    return key

def cleanup_cache():
    """Periodically clean up the cache to prevent memory issues"""
    current_time = time.time()

    # Only run cleanup if it's been at least an hour since last cleanup
    if 'last_cleanup' not in background_cache or current_time - background_cache.get('last_cleanup', 0) < 3600:
        return

    print(f"Running cache cleanup. Current size: {len(background_cache)}")

    # Filter out non-cache entries and expired entries
    cache_entries = {
        k: v for k, v in background_cache.items()
        if k != 'last_cleanup' and isinstance(v, dict) and 'timestamp' in v
        and current_time - v['timestamp'] <= CACHE_TTL
    }

    # If we're still over size limit, remove oldest entries
    if len(cache_entries) > CACHE_MAX_SIZE:
        # Sort by timestamp (oldest first) and keep only newest MAX_SIZE entries
        sorted_keys = sorted(cache_entries.keys(),
                            key=lambda k: cache_entries[k]['timestamp'])

        # Keep only the newest entries up to the maximum size
        keep_keys = set(sorted_keys[-CACHE_MAX_SIZE:])
        cache_entries = {k: v for k, v in cache_entries.items() if k in keep_keys}

    # Update the cache with cleaned entries and set last cleanup time
    background_cache.clear()
    background_cache.update(cache_entries)
    background_cache['last_cleanup'] = current_time

    print(f"Cache cleanup complete. New size: {len(background_cache)-1}")

    # Also clean up article content cache
    cleanup_article_content_cache()

def cleanup_article_content_cache():
    """Clean up the article content cache to prevent memory issues"""
    # If the cache is small, no need to clean up
    if len(article_content_cache) <= CACHE_MAX_SIZE:
        return

    print(f"Cleaning up article content cache. Current size: {len(article_content_cache)}")

    # If we're over the size limit, remove random entries to bring it down to 80% of max size
    if len(article_content_cache) > CACHE_MAX_SIZE:
        # Calculate how many entries to remove
        target_size = int(CACHE_MAX_SIZE * 0.8)  # Keep 80% of max size
        entries_to_remove = len(article_content_cache) - target_size

        # Get a list of keys and remove random entries
        keys = list(article_content_cache.keys())
        import random
        keys_to_remove = random.sample(keys, entries_to_remove)

        # Remove the selected entries
        for key in keys_to_remove:
            article_content_cache.pop(key, None)

    print(f"Article content cache cleanup complete. New size: {len(article_content_cache)}")

# Helper function to format article descriptions as complete sentences
def format_description(description):
    """
    Ensure article descriptions are complete sentences with proper formatting.
    Limits descriptions to 3-4 sentences maximum.

    Args:
        description: The original description text

    Returns:
        str: A well-formatted description ending with proper punctuation
    """
    if not description or description.strip() == "":
        return 'Click "Summarize" to generate an AI-powered summary of this article.'

    # Clean up the description
    formatted_desc = description.strip()

    # Remove common location prefixes in Malaysian/Asian news articles
    formatted_desc = re.sub(r'^([A-Z\s]+):\s+', '', formatted_desc)

    # Try to decode HTML entities that might be present
    try:
        import html
        formatted_desc = html.unescape(formatted_desc)
    except (ImportError, AttributeError):
        pass  # If html module is not available, continue with other methods

    # Fix common encoding issues like "â" characters from curly quotes
    formatted_desc = formatted_desc.replace('â', "'")  # Replace â with apostrophe
    formatted_desc = re.sub(r'â\s*â', '"', formatted_desc)  # Replace âpairâ with quote
    formatted_desc = re.sub(r'â([^â]*?)â', r'"\1"', formatted_desc)  # Replace âtextâ with "text"

    # Handle hyphenated words with encoding issues like "âfreedom-focusedâ"
    formatted_desc = re.sub(r'â([a-zA-Z0-9\s-]+?)â', r'"\1"', formatted_desc)

    # More aggressive handling for quote-like characters
    # This handles cases where different encoding issues mix in the same text
    formatted_desc = re.sub(r'[â""]([^â""]+?)[â""]', r'"\1"', formatted_desc)

    # Replace other common encoding issues
    replacements = {
        'â€™': "'",    # Right single quotation mark
        'â€"': "–",    # En dash
        'â€"': "—",    # Em dash
        'â€œ': '"',    # Left double quotation mark
        'â€': '"',     # Right double quotation mark
        'Â': '',       # Non-breaking space often appears as Â
        'â€¦': '...',  # Ellipsis
        'Ã©': 'é',     # é with accent
        'Ã¨': 'è',     # è with accent
        'Ã¢': 'â',     # â with circumflex
        'Ã´': 'ô',     # ô with circumflex
        'Ã»': 'û',     # û with circumflex
        'Ã®': 'î',     # î with circumflex
        'Ã±': 'ñ',     # ñ with tilde
        'â€˜': "'",    # Left single quotation mark
        'â€™': "'",    # Right single quotation mark
        'Ã¡': 'á',     # á with accent
        'Ã³': 'ó',     # ó with accent
        'Ãº': 'ú',     # ú with accent
        'Ã': 'í',      # í with accent
        'â™': '♥',     # Heart symbol
        '&amp;': '&',  # Ampersand
        '&lt;': '<',   # Less than
        '&gt;': '>',   # Greater than
        '&quot;': '"', # Quote
        '&apos;': "'", # Apostrophe
    }

    for bad, good in replacements.items():
        formatted_desc = formatted_desc.replace(bad, good)

    # Try Unicode normalization to fix remaining encoding issues
    try:
        import unicodedata
        formatted_desc = unicodedata.normalize('NFKD', formatted_desc)
    except (ImportError, TypeError):
        pass  # If unicodedata module is not available, continue with other methods

    # Remove control characters and other non-printable characters
    formatted_desc = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', formatted_desc)

    # Final cleanup pass for any persistent encoding issues
    # Replace any unusual Unicode punctuation with standard ASCII equivalents
    unusual_chars = {
        '"': '"',  # Left double quotation mark
        '"': '"',  # Right double quotation mark
        ''': "'",  # Left single quotation mark
        ''': "'",  # Right single quotation mark
        '—': '-',  # Em dash
        '–': '-',  # En dash
        '…': '...', # Ellipsis
        '•': '*',  # Bullet
        '·': '*',  # Middle dot
        '′': "'",  # Prime
        '″': '"',  # Double prime
        '„': '"',  # Double low-9 quotation mark
        '‟': '"',  # Double high-reversed-9 quotation mark
        '‹': '<',  # Single left-pointing angle quotation mark
        '›': '>',  # Single right-pointing angle quotation mark
        '«': '<<', # Left-pointing double angle quotation mark
        '»': '>>', # Right-pointing double angle quotation mark
        '≤': '<=', # Less-than or equal to
        '≥': '>=', # Greater-than or equal to
        '≠': '!=', # Not equal to
        '±': '+/-', # Plus-minus sign
    }

    for unusual, replacement in unusual_chars.items():
        formatted_desc = formatted_desc.replace(unusual, replacement)

    # Second pass for any double-encoded characters that might still remain
    # This helps with cases where a single encoding fix might expose another encoding issue
    if 'â' in formatted_desc or 'Ã' in formatted_desc or '&' in formatted_desc and ';' in formatted_desc:
        # Reapply key replacements for persistent problematic characters
        formatted_desc = formatted_desc.replace('â', "'")
        formatted_desc = re.sub(r'â([a-zA-Z0-9\s-]+?)â', r'"\1"', formatted_desc)

        # Try HTML unescape again for any newly exposed HTML entities
        try:
            import html
            formatted_desc = html.unescape(formatted_desc)
        except (ImportError, AttributeError):
            pass

    # Handle cases with obvious cut-off text that looks like "Read more..."
    if re.search(r'\b(read\s+more|continue\s+reading|see\s+more|find\s+out\s+more|more\s+at|full\s+story)\b',
                formatted_desc.lower()):
        # Remove the "read more" part and anything that follows
        formatted_desc = re.sub(r'\b(read\s+more|continue\s+reading|see\s+more|find\s+out\s+more|more\s+at|full\s+story).*$',
                              '', formatted_desc, flags=re.IGNORECASE).strip()

    # Additional check for incomplete sentences ending with "Read.." or similar patterns
    formatted_desc = re.sub(r'(?:\s+|^)(Read|Click|See|View|Visit|Check|More)\.{1,3}$', '.', formatted_desc, flags=re.IGNORECASE)

    # Check for sentences ending with a single word followed by periods
    formatted_desc = re.sub(r'\s+\w+\.{2,}$', '.', formatted_desc)

    # Remove tracking parameters or source markers often found at the end
    formatted_desc = re.sub(r'\s*\(source:.*?\)\s*$', '', formatted_desc, flags=re.IGNORECASE)
    formatted_desc = re.sub(r'\s*\[.*?\]\s*$', '', formatted_desc, flags=re.IGNORECASE)
    formatted_desc = re.sub(r'\s*\-\s*[A-Za-z0-9\s]+$', '', formatted_desc)

    # Remove any trailing colons, ellipsis, etc.
    formatted_desc = re.sub(r'[:;,…]+\s*$', '', formatted_desc)

    # Check for sentences that end with words that suggest an incomplete thought
    fragment_endings = r'\b(and|but|or|as|if|the|a|an|to|with|by|for|in|on|at|of|from|that|which|who|whom|whose|when|where|why|how|what|this|these|those|such|than)\s*[.!?]?$'

    # If the sentence ends with a fragment marker, either:
    if re.search(fragment_endings, formatted_desc, re.IGNORECASE):
        # 1. Try to find a good sentence break earlier in the text
        sentence_parts = re.split(r'([.!?])\s+', formatted_desc)
        if len(sentence_parts) > 2:  # We have multiple sentences
            # Join all but the last (incomplete) sentence
            reconstructed = ''
            for i in range(0, len(sentence_parts) - 2, 2):
                reconstructed += sentence_parts[i] + sentence_parts[i+1] + ' '
            formatted_desc = reconstructed.strip()
        else:
            # 2. If we can't find a good break, remove the trailing fragment
            formatted_desc = re.sub(fragment_endings, '.', formatted_desc, flags=re.IGNORECASE)

    # Make sure we still have content after all that processing
    if not formatted_desc.strip():
        return 'Click "Summarize" to generate an AI-powered summary of this article.'

    # Check if the description ends with proper sentence punctuation
    if not re.search(r'[.!?]$', formatted_desc):
        # If it doesn't end with proper punctuation, add a period
        formatted_desc = formatted_desc + '.'

    # Ensure the first letter is capitalized
    if formatted_desc and len(formatted_desc) > 0:
        formatted_desc = formatted_desc[0].upper() + formatted_desc[1:]

    # Replace multiple spaces with a single space
    formatted_desc = re.sub(r'\s+', ' ', formatted_desc)

    # Remove any double periods that might have been created
    formatted_desc = re.sub(r'\.{2,}', '.', formatted_desc)
    formatted_desc = re.sub(r'\.\s*\.', '.', formatted_desc)

    # Ensure there's only a single period at the end
    formatted_desc = re.sub(r'\.+$', '.', formatted_desc)

    # Split into sentences to limit to 3-4 sentences
    sentences = re.split(r'([.!?])\s+', formatted_desc)

    # Filter out likely promotional sentences before limiting
    promotional_patterns = [
        r'buy\s+now', r'order\s+now', r'click\s+here', r'sale', r'discount', r'offer',
        r'limited\s+time', r'special\s+offer', r'best\s+price', r'official\s+website',
        r'free\s+shipping', r'money\s+back', r'guarantee', r'promotion', r'coupon',
        r'discount\s+code', r'promo\s+code', r'save\s+\d+%', r'lowest\s+price',
        r'sponsored', r'advertisement', r'promoted', r'advertorial',
        r'press\s+release', r'product\s+review', r'affiliate', r'partnership',
        r'legal\s+steroid', r'weight\s+loss', r'weight\s+gain', r'supplement',
        r'miracle', r'revolutionary', r'breakthrough', r'doctor\s+recommended',
        r'\$\d+', r'€\d+', r'£\d+', r'\d+% off', r'free\s+trial'
    ]

    filtered_sentences = []
    for i in range(0, len(sentences), 2):
        if i + 1 < len(sentences):
            sentence = sentences[i] + sentences[i+1]
            # Check if sentence contains promotional content
            is_promotional = any(re.search(pattern, sentence.lower()) for pattern in promotional_patterns)
            if not is_promotional:
                filtered_sentences.append(sentences[i])
                filtered_sentences.append(sentences[i+1])
        elif i < len(sentences):
            # Handle the last sentence if it doesn't have punctuation
            is_promotional = any(re.search(pattern, sentences[i].lower()) for pattern in promotional_patterns)
            if not is_promotional:
                filtered_sentences.append(sentences[i])

    # If we still have sentences after filtering out promotional content
    if filtered_sentences:
        sentences = filtered_sentences

    # If we have more than 3-4 sentences (each sentence is represented by two elements in the list: content + punctuation)
    if len(sentences) > 8:  # More than 4 sentences
        # Keep only first 3-4 sentences (up to 8 elements in our split list)
        limited_desc = ''
        for i in range(0, min(8, len(sentences)), 2):
            if i + 1 < len(sentences):
                limited_desc += sentences[i] + sentences[i+1] + ' '
            else:
                limited_desc += sentences[i] + '.'
        formatted_desc = limited_desc.strip()
    else:
        # Reconstruct the description from filtered sentences
        limited_desc = ''
        for i in range(0, len(sentences), 2):
            if i + 1 < len(sentences):
                limited_desc += sentences[i] + sentences[i+1] + ' '
            else:
                limited_desc += sentences[i] + '.'
        formatted_desc = limited_desc.strip()

    # Remove promotional markers and URLs
    formatted_desc = re.sub(r'\bSPONSORED\b|\bADVERTISEMENT\b|\bPROMOTED\b', '', formatted_desc, flags=re.IGNORECASE)
    formatted_desc = re.sub(r'https?://\S+', '', formatted_desc)

    # Final cleanup of any doubled spaces
    formatted_desc = re.sub(r'\s+', ' ', formatted_desc).strip()

    # Final check for complete sentences
    # Remove any trailing fragments that don't end with proper punctuation
    formatted_desc = re.sub(r'\s+\w+\s*$', '.', formatted_desc)

    # Make sure it ends with a single period
    if not formatted_desc.endswith('.') and not formatted_desc.endswith('!') and not formatted_desc.endswith('?'):
        formatted_desc += '.'

    # Remove any multiple periods that might have been created
    formatted_desc = re.sub(r'\.{2,}$', '.', formatted_desc)

    return formatted_desc

# Add middleware to redirect HTTP to HTTPS in production
@main.before_request
def enforce_https():
    # Only enforce in production (Heroku)
    if os.environ.get('DYNO') and request.headers.get('X-Forwarded-Proto') == 'http':
        url = request.url.replace('http://', 'https://', 1)
        return redirect(url, code=301)

@main.route('/')
def index():
    # Serve index.html directly without any authentication checks
    # Use the separated topics lists
    news_topics = NEWS_TOPICS
    newsletter_topics = NEWSLETTER_TOPICS

    # Format premium source names for display (convert IDs to readable names)
    premium_sources = [
        # US sources
        'The New York Times', 'The Washington Post', 'The Wall Street Journal',
        'CNN', 'Fox News', 'USA Today',
        'ABC News', 'NBC News', 'CBS News', 'CNBC',
        'Bloomberg', 'Politico', 'Reuters', 'Associated Press',
        'NPR', 'The Atlantic', 'TIME',

        # UK sources
        'BBC News', 'The Guardian', 'The Telegraph',
        'The Independent', 'Financial Times', 'The Economist',
        'Daily Mail', 'The Mirror', 'The Sun', 'Sky News',

        # Tech/Science sources from US/UK
        'The Verge', 'Wired', 'Ars Technica',
        'New Scientist', 'Nature', 'Science', 'Scientific American'
    ]

    return render_template('index.html',
                         news_topics=news_topics,
                         newsletter_topics=newsletter_topics,
                         youtube_channels=YOUTUBE_CHANNELS,
                         topics=ALLOWED_TOPICS,  # Keep for backward compatibility
                         premium_sources=premium_sources)

@main.route('/welcome')
def welcome():
    # Redirect to index since we're bypassing email verification completely
    return redirect(url_for('main.index'))

@main.route('/register', methods=['POST'])
def register():
    # Bypass registration completely
    return redirect(url_for('main.index'))

@main.route('/verify/<token>')
def verify_email(token):
    # Bypass verification completely
    return redirect(url_for('main.index'))

@main.route('/resend-verification/<email>')
def resend_verification(email):
    # Bypass verification completely
    return redirect(url_for('main.index'))

@main.route('/news')
def get_news():
    """Get news articles for a specific topic."""
    # Get topic from query string, defaulting to General
    topic = request.args.get('topic', 'Headlines')
    source_type = request.args.get('source_type', 'news')  # 'news' or 'newsletter'

    # Validate that the topic is in our allowed list
    if topic not in ALLOWED_TOPICS:
        print(f"WARNING: Invalid topic requested: {topic}. Defaulting to Headlines.")
        topic = 'Headlines'  # Default to Headlines for invalid topics

    print(f"News request for topic: {topic}, source_type: {source_type}")

    try:
        # Determine if this is a newsletter topic request based on source_type parameter
        is_newsletter_request = (source_type == 'newsletter')
        print(f"is_newsletter_request: {is_newsletter_request} (based on source_type: {source_type})")

        # Get articles from the appropriate service
        if is_newsletter_request:
            print(f"Fetching newsletter content for topic: {topic}")
            # Map the topic to the appropriate RSS feed category
            # For Technology newsletters, use 'technology' RSS feeds
            if topic.lower() == 'technology':
                print("Using 'technology' RSS feeds for newsletter content")
                api_response = news_service._fetch_from_rss('technology')
            else:
                # For other newsletter topics, try to find matching RSS feeds
                print(f"Using '{topic.lower()}' RSS feeds for newsletter content")
                api_response = news_service._fetch_from_rss(topic.lower())
        else:
            print(f"Fetching news articles for topic: {topic} using NewsData API")
            # Use NewsData API for regular news
            api_response = news_service.get_articles(topic)

        # Check if we got a valid response with articles
        if not api_response:
            print(f"No API response for topic: {topic}")
            return jsonify([]), 200  # Return empty list instead of error

        if not isinstance(api_response, dict):
            print(f"Invalid API response format: {type(api_response)}")
            return jsonify([]), 200

        if 'results' not in api_response:
            print(f"No results key in API response: {api_response.keys()}")
            return jsonify([]), 200

        # Extract the articles from the response
        articles = api_response.get('results', [])
        print(f"Got {len(articles)} articles from {'RSS feeds' if is_newsletter_request else 'API'} for topic: {topic}")

        # Process all articles without source filtering
        processed_articles = []

        for article in articles:
            try:
                # Skip articles without required fields
                if not article.get('title') or not article.get('link'):
                    continue

                # Check if article is from today (within last 24 hours)
                is_today = is_recent_article(article.get('pubDate'), hours=24)

                # Improve image URL if available
                image_url = article.get('image_url', '')
                if image_url:
                    # Try to upgrade to higher resolution version
                    image_url = image_url.replace('&width=300', '&width=1200')
                    image_url = image_url.replace('&height=200', '&height=800')
                    image_url = image_url.replace('width=300', 'width=1200')
                    image_url = image_url.replace('w=300', 'w=1200')
                    image_url = image_url.replace('h=200', 'h=800')
                    # For some CDNs that use resize parameters in the URL
                    if 'resize=' in image_url:
                        image_url = re.sub(r'resize=\d+x\d+', 'resize=1200x800', image_url)

                # Get article content and description from API
                api_content = article.get('content', '')
                description = article.get('description', '')

                # Cache the article content by URL if it's available
                if api_content and len(api_content) > 150:
                    # Create a cache key for this article's content
                    article_cache_key = generate_article_content_key(article['link'])
                    if article_cache_key:
                        article_content_cache[article_cache_key] = api_content
                        print(f"Cached article content for URL: {article['link']} ({len(api_content)} characters)")

                # Process article with essential information for frontend
                article_data = {
                    'id': hashlib.md5(article['link'].encode()).hexdigest(),
                    'title': article['title'],
                    'url': article['link'],
                    'source': article.get('source_name', article.get('source_id', 'Unknown')),
                    'published_at': format_date(article.get('pubDate')),
                    'published_at_raw': article.get('pubDate', ''),  # Store raw date for sorting
                    'is_recent': is_recent_article(article.get('pubDate')),
                    'is_today': is_today,  # Store whether article is from today
                    'short_description': format_description(description or api_content),
                    'image_url': image_url,  # Use the potentially upgraded image URL
                    'topic': topic,
                    'source_type': 'newsletter' if is_newsletter_request else 'news'  # Add source type for frontend
                }

                processed_articles.append(article_data)
            except Exception as article_error:
                print(f"Error processing article: {str(article_error)}")
                continue

        print(f"Processed {len(processed_articles)} articles for topic: {topic}")

        # If no articles were processed, return empty list
        if not processed_articles:
            print(f"No articles available for topic: {topic}")
            return jsonify([]), 200

        # Sort by date (latest first)
        processed_articles.sort(key=lambda x: x.get('published_at_raw', ''), reverse=True)

        print(f"Returning {len(processed_articles)} articles for topic: {topic}")
        return jsonify(processed_articles), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching articles: {str(e)}")
        import traceback
        traceback.print_exc()
        print(f"Error in get_news: {str(e)}")
        return jsonify([]), 200  # Return empty list instead of error to avoid UI issues

@main.route('/youtube')
def get_youtube_videos():
    """Get YouTube videos from a specific channel with caching."""
    # Get channel from query string, defaulting to 'bytebytego'
    channel = request.args.get('channel', 'bytebytego')
    force_refresh = request.args.get('refresh', 'false').lower() == 'true'

    print(f"YouTube request for channel: {channel}, force_refresh: {force_refresh}")

    try:
        # Get videos from YouTube service
        videos = youtube_service.get_videos(channel)

        # Process videos for frontend
        processed_videos = []
        for video in videos:
            try:
                video_data = {
                    'id': video['video_id'],
                    'title': video['title'],
                    'url': video['video_url'],
                    'source': video['source'],
                    'published_at': format_date(video['publish_date']),
                    'published_at_raw': video['publish_date'],
                    'is_recent': is_recent_article(video['publish_date']),
                    'short_description': format_description(video['description']),
                    'image_url': video['thumbnail_url'],
                    'topic': 'Technology',  # YouTube videos are typically tech-related
                    'source_type': 'youtube',
                    'duration': video.get('duration', 'N/A'),
                    'view_count': video.get('view_count', 'N/A'),
                    'channel_title': video.get('channel_title', ''),
                    'video_metadata': video  # Store full metadata for summarization
                }
                processed_videos.append(video_data)
            except Exception as video_error:
                print(f"Error processing video: {str(video_error)}")
                continue

        print(f"Returning {len(processed_videos)} videos for channel: {channel}")
        return jsonify(processed_videos), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching YouTube videos: {str(e)}")
        print(f"Error in get_youtube_videos: {str(e)}")
        return jsonify([]), 200  # Return empty list instead of error to avoid UI issues

@main.route('/summarize', methods=['GET', 'POST'])
def summarize_article():
    """Summarize a news article with AI."""

    try:
        # Get parameters from either GET or POST request
        if request.method == 'POST':
            data = request.get_json()
            url = data.get('url', '')
            title = data.get('title', '')
            force_refresh = data.get('refresh', False)
            include_image = data.get('include_image', False)
            description = data.get('description', '')
            api_image_url = data.get('image_url', '')
        else:
            url = request.args.get('url', '')
            title = request.args.get('title', '')
            force_refresh = request.args.get('refresh', 'false').lower() == 'true'
            include_image = request.args.get('include_image', 'false').lower() == 'true'
            description = request.args.get('description', '')
            api_image_url = request.args.get('image_url', '')

        if not url and not title:
            return jsonify({'error': 'URL or title is required'}), 400



        # Log the request parameters
        print(f"Summarize request - URL: {url}, Title: {title}, Include Image: {include_image}")
        print(f"Description length: {len(description)}")

        # Create a cache key using our standardized function
        cache_key = generate_cache_key("summary", url, title, "with_image" if include_image else "")

        # Periodically clean up the cache
        cleanup_cache()

        # Acquire lock for thread-safe cache operations
        with background_cache_lock:
            # Check cache first (unless forced refresh requested)
            if not force_refresh and cache_key in background_cache:
                cache_entry = background_cache[cache_key]

                # If the summary is already generated (not pending), return it immediately
                if 'summary_data' in cache_entry and not cache_entry.get('summary_pending', False):
                    print(f"Summary cache hit for: {title if title else url} - returning immediately")
                    # Update timestamp to mark as recently accessed
                    background_cache[cache_key]['timestamp'] = time.time()
                    cached_result = cache_entry['summary_data']
                    # If no extraction_method in cached result, add one
                    if 'extraction_method' not in cached_result:
                        cached_result['extraction_method'] = "From Cache"
                    return jsonify(cached_result)

                # If it's currently being generated, tell the frontend to use WebSocket
                if cache_entry.get('summary_pending', False):
                    # Check if there's an active generation process with a valid start time
                    elapsed = 0
                    if 'start_time' in cache_entry:
                        elapsed = time.time() - cache_entry['start_time']

                    # Only consider it active if started in the last 5 minutes (prevents stuck processes)
                    if elapsed < 300:  # 5 minutes
                        print(f"Summary for {title if title else url} is currently being generated (started {elapsed:.1f}s ago). Using WebSocket for updates.")
                        return jsonify({
                            'summary_pending': True,
                            'message': 'Summary is being generated, use WebSocket for updates.',
                            'cache_key': cache_key,
                            'elapsed_seconds': int(elapsed)
                        })
                    else:
                        print(f"Found stale summary generation process for {title if title else url} (started {elapsed:.1f}s ago). Starting fresh generation.")
                        # Fall through to start a new generation

            # Look for similar cache entries for the same title/url (might have different parameters)
            title_key = generate_cache_key("summary", url, title)
            for existing_key in background_cache.keys():
                if existing_key.startswith(title_key) and existing_key != cache_key:
                    existing_entry = background_cache[existing_key]
                    # Check if it's complete and has summary data
                    if ('summary_data' in existing_entry and
                        not existing_entry.get('summary_pending', False)):
                        print(f"Found existing summary for title '{title if title else url}' with different cache key.")
                        print(f"Using existing summary instead of generating new one.")

                        # Copy the summary to our cache key and return it
                        background_cache[cache_key] = {
                            'summary_data': existing_entry['summary_data'],
                            'summary_pending': False,
                            'from_cache': True,
                            'timestamp': time.time(),
                            'original_key': existing_key
                        }

                        return jsonify(existing_entry['summary_data'])

            # Initialize an entry in the cache to mark the start of processing
            # This happens while we still hold the lock, preventing race conditions
            background_cache[cache_key] = {
                'start_time': time.time(),
                'summary_pending': True,
                'thread_id': None  # Will be set by the thread when it starts
            }

            # Log that we're starting a new thread
            print(f"Starting background thread to generate summary for {title if title else url}")

        # Start a background thread to generate the summary
        # We can release the lock before starting the thread
        summary_thread = threading.Thread(
            target=generate_summary_async,
            args=(url, title, description, "", api_image_url, include_image, cache_key),
            daemon=True
        )
        summary_thread.start()

        # Return status that indicates it's being generated
        return jsonify({
            'summary_pending': True,
            'complete': False,
            'elapsed_seconds': 0,
            'cache_key': cache_key
        })

    except Exception as e:
        current_app.logger.error(f"Error generating summary: {str(e)}")
        # Return error response with 200 status code to prevent frontend error handling
        return jsonify({
            'article_summary': f'An error occurred while generating the summary. {description if "description" in locals() and description else "Please try again later."}',
            'key_points': ['Please try again later or refer to the original article.'],
            'extraction_method': 'Unknown (error occurred)'
        }), 200  # Changed from 500 to 200


def generate_summary_async(url, title, description, api_content, api_image_url, include_image, cache_key):
    """Generate summary asynchronously and store in cache."""
    # Get thread ID for logging
    thread_id = threading.current_thread().ident

    # Acquire lock to update cache with thread ID
    with background_cache_lock:
        # Double-check that this generation is still needed
        if cache_key in background_cache:
            # Check if another thread has already completed this work
            if 'summary_data' in background_cache[cache_key] and not background_cache[cache_key].get('summary_pending', True):
                print(f"Thread #{thread_id}: Summary generation already completed for {title if title else url}, exiting.")
                return

            # Update the thread ID in the cache
            background_cache[cache_key]['thread_id'] = thread_id

            # Log thread start
            print(f"====== STARTED SUMMARY THREAD #{thread_id} for '{title if title else url}' ({cache_key}) ======")

    try:
        start_time = time.time()

        # Emit WebSocket event to notify clients that processing has started
        socketio.emit('summary_update', {
            'status': 'started',
            'cache_key': cache_key,
            'message': 'Started generating summary',
            'thread_id': thread_id
        }, namespace='/summary')

        # Initialize variables
        article_content = ""
        image_url = ""
        content_extraction_failed = False
        extraction_method = "Unknown"

        # Check if this is a YouTube video URL
        is_youtube_video = url and ('youtube.com/watch' in url or 'youtu.be/' in url)

        if is_youtube_video:
            print(f"Detected YouTube video URL: {url}")
            # Extract video ID from URL
            video_id = youtube_service.extract_video_id(url)

            if video_id:
                try:
                    # Emit progress update
                    socketio.emit('summary_update', {
                        'status': 'progress',
                        'cache_key': cache_key,
                        'message': 'Extracting YouTube video content',
                        'progress': 20
                    }, namespace='/summary')

                    # Use the comprehensive content extraction method
                    article_content = youtube_service.get_video_content_for_summarization(
                        video_id=video_id,
                        title=title
                    )

                    # Check if we got transcript content
                    if "Transcript (Language:" in article_content:
                        extraction_method = "YouTube video with transcript"
                        print(f"Using YouTube video with transcript for summarization: {len(article_content)} characters")
                    else:
                        extraction_method = "YouTube video metadata only"
                        print(f"Using YouTube video metadata only (no transcript): {len(article_content)} characters")

                except Exception as youtube_error:
                    print(f"Error fetching YouTube video content: {youtube_error}")
                    # Fallback to basic content
                    api_content = description if description else f"YouTube video: {title}"
                    article_content = f"Title: {title}\n\nDescription: {api_content}"
                    extraction_method = "YouTube video description (fallback)"

        # IMAGE HANDLING - PRIORITY 1: Always try to get image from API first
        if include_image and api_image_url:
            image_url = api_image_url
            print(f"Using image URL from NewsData API: {image_url}")

        # Skip normal content extraction for YouTube videos since we already have the content
        if not is_youtube_video:
            # Emit progress update
            socketio.emit('summary_update', {
                'status': 'progress',
                'cache_key': cache_key,
                'message': 'Extracting article content',
                'progress': 30
            }, namespace='/summary')

            # PRIORITY 1: Check if we have cached content for this URL
            if url:
                article_cache_key = generate_article_content_key(url)
                if article_cache_key and article_cache_key in article_content_cache:
                    api_content = article_content_cache[article_cache_key]
                    article_content = f"Title: {title}\n\n{api_content}"
                    extraction_method = "Cached NewsData API content"
                    print(f"Using cached content for URL: {url} ({len(article_content)} characters)")

            # PRIORITY 2: Fall back to newspaper3k extraction if API content not available
            if not article_content and url:
                print("API content insufficient, falling back to newspaper3k extraction")
                try:
                    # Extract article content from URL
                    if include_image and not image_url:
                        # If we need an image and don't have one from the API, extract both content and image
                        extraction_result = summarizer.extract_content(url, fetch_image=True) if url else {"content": "", "image_url": ""}
                        article_content = extraction_result.get("content", "")

                        # IMAGE HANDLING - PRIORITY 2: Get image from newspaper3k if API image wasn't available
                        if not image_url:  # Only use extracted image if we don't already have one from the API
                            image_url = extraction_result.get("image_url", "")
                            if image_url:
                                print(f"Using image URL from newspaper3k extraction: {image_url}")

                        # Track extraction method
                        extraction_method = extraction_result.get("extraction_method", "Unknown")
                    else:
                        # Just extract content if we don't need an image or already have one from the API
                        article_content = summarizer.extract_content(url) if url else ""
                        # Track extraction method
                        extraction_method = "newspaper3k" if article_content and len(article_content) > 150 else "Unknown"

                    print(f"Article content extracted via newspaper3k: {len(article_content)} characters")

                except Exception as e:
                    print(f"Error extracting content with newspaper3k: {e}")
                    content_extraction_failed = True

            # PRIORITY 3: Use API description as fallback if no content extracted
            if not article_content and description:
                print("Using API description as fallback content")
                api_content = description
                article_content = f"Title: {title}\n\nDescription: {api_content}"
                extraction_method = "API description (fallback)"

        else:
            # For YouTube videos, use thumbnail as image if requested
            if include_image and not image_url:
                # Try to get thumbnail from YouTube URL
                if 'youtube.com/watch' in url:
                    video_id = url.split('v=')[1].split('&')[0] if 'v=' in url else None
                elif 'youtu.be/' in url:
                    video_id = url.split('youtu.be/')[1].split('?')[0] if 'youtu.be/' in url else None

                if video_id:
                    image_url = f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg"
                    print(f"Using YouTube thumbnail as image: {image_url}")

        # If content extraction failed completely (all methods), return a clear error
        if content_extraction_failed and not article_content:
            error_summary = {
                "article_summary": "Unable to access the full article content. Please click 'Read Article' to view the original article.",
                "key_points": [
                    "The article content could not be retrieved for accurate summarization.",
                    "This could be due to paywall restrictions, subscription requirements, or technical issues.",
                    "For complete information, please read the original article."
                ],
                "extraction_method": extraction_method
            }

            if include_image and image_url:
                error_summary['image_url'] = image_url

            # Cache this result and emit completion
            with background_cache_lock:
                background_cache[cache_key] = {
                    'summary_data': error_summary,
                    'summary_pending': False,
                    'timestamp': time.time()
                }

            # Emit completion event
            socketio.emit('summary_update', {
                'status': 'completed',
                'cache_key': cache_key,
                'summary_data': error_summary,
                'message': 'Summary generation completed (content extraction failed)'
            }, namespace='/summary')

            return

        # Emit progress update for summarization
        socketio.emit('summary_update', {
            'status': 'progress',
            'cache_key': cache_key,
            'message': 'Generating AI summary',
            'progress': 70
        }, namespace='/summary')

        # If we have content, proceed with summarization
        print(f"Summarizing article: {title}")

        # Wrap the summarization call in a try-catch block
        try:
            summary_data = summarizer.generate_summary(article_content, title=title)
        except Exception as summarization_error:
            print(f"Error during summarization: {summarization_error}")
            # Use fallback summary but still return 200 status
            summary_data = {
                "article_summary": f"Unable to generate a detailed summary for this article. {description if description else 'Please click Read Article to view the original content.'}",
                "key_points": [
                    "Summary generation encountered technical difficulties.",
                    "The article content may be complex or the AI service temporarily unavailable.",
                    "Please refer to the original article for complete information."
                ],
                "extraction_method": f"{extraction_method} (summarization failed)"
            }

        # Add image URL if it was requested and available
        if include_image and image_url:
            summary_data['image_url'] = image_url

        # Add extraction method for debugging
        summary_data['extraction_method'] = extraction_method

        # Clean up key points to ensure they're properly formatted
        if 'key_points' in summary_data:
            cleaned_points = []
            for point in summary_data['key_points']:
                if isinstance(point, str):
                    clean_point = re.sub(r'^```(?:json|html|markdown)?\s*', '', point)
                    clean_point = re.sub(r'```$', '', clean_point)
                    # Remove any bullet point markers that might be included
                    clean_point = re.sub(r'^[\s•\-\*]+', '', clean_point)
                    cleaned_points.append(clean_point.strip())
            summary_data['key_points'] = cleaned_points

        # Store in shared background_cache with timestamp for consistent caching
        with background_cache_lock:
            background_cache[cache_key] = {
                'summary_data': summary_data,
                'summary_pending': False,
                'timestamp': time.time(),
                'complete': True
            }

        duration = time.time() - start_time
        print(f"====== COMPLETED SUMMARY THREAD #{thread_id} for '{title if title else url}' in {duration:.2f}s ======")

        # Emit completion event
        socketio.emit('summary_update', {
            'status': 'completed',
            'cache_key': cache_key,
            'summary_data': summary_data,
            'message': f'Summary generation completed in {duration:.1f}s',
            'duration': duration
        }, namespace='/summary')

    except Exception as e:
        print(f"Error in summary generation thread #{thread_id}: {str(e)}")

        # Create error summary
        error_summary = {
            "article_summary": f"An error occurred while generating the summary. {description if description else 'Please try again later.'}",
            "key_points": ["Please try again later or refer to the original article."],
            "extraction_method": "Error occurred during processing"
        }

        # Store error result in cache
        with background_cache_lock:
            background_cache[cache_key] = {
                'summary_data': error_summary,
                'summary_pending': False,
                'timestamp': time.time(),
                'error': True
            }

        # Emit error event
        socketio.emit('summary_update', {
            'status': 'error',
            'cache_key': cache_key,
            'summary_data': error_summary,
            'message': 'Summary generation encountered an error',
            'error': str(e)
        }, namespace='/summary')

@main.route('/background', methods=['GET', 'POST'])
def generate_background():
    """Generate enriched context for an article in the background."""
    # Log request method
    print(f"========== BACKGROUND CONTEXT REQUEST ({request.method}) ==========")

    # Get parameters based on request method
    if request.method == 'POST':
        data = request.json if request.is_json else {}
        print(f"POST request with data: {json.dumps(data)[:200]}")
    else:
        data = {
            'title': request.args.get('title', ''),
            'topic': request.args.get('topic', ''),
            'summary': request.args.get('summary', '{}'),
            'url': request.args.get('url', '')
        }
        print(f"GET request with params: {json.dumps(data)[:200]}")

    # Get the title, topic, and URL from the request
    title = data.get('title', '')
    topic = data.get('topic', '')
    summary_json = data.get('summary', '{}')
    url = data.get('url', '')  # Get URL from request

    # Validate input
    if not title:
        return jsonify({
            'error': 'Title is required',
            'message': 'Please provide a title to generate background context.'
        }), 400

    # If topic is provided, check if it's valid
    if topic and topic not in ALLOWED_TOPICS:
        # Log warning for invalid topic
        print(f"Warning: Invalid topic '{topic}' provided for background context. Using empty topic.")
        # Set topic to empty string instead of using invalid input
        topic = ''

    # Create a unique cache key
    cache_key = generate_cache_key("background", title, topic, url)

    # Acquire lock for thread-safe cache operations
    with background_cache_lock:
        # OPTIMIZATION: Check if the content already exists in the cache and return immediately if it does
        if cache_key in background_cache:
            cache_entry = background_cache[cache_key]

            # If the content is already generated (not pending), return it immediately
            if 'enriched_content' in cache_entry and not cache_entry.get('context_pending', False):
                print(f"Background cache hit for: {title} - returning immediately without polling")
                # Update timestamp to mark as recently accessed
                background_cache[cache_key]['timestamp'] = time.time()
                return jsonify({
                    'enriched_content': cache_entry['enriched_content'],
                    'context_pending': False,
                    'from_cache': True
                })

            # If it's currently being generated, tell the frontend to use WebSocket
            if cache_entry.get('context_pending', False):
                # Check if there's an active generation process with a valid start time
                elapsed = 0
                if 'start_time' in cache_entry:
                    elapsed = time.time() - cache_entry['start_time']

                # Only consider it active if started in the last 10 minutes (prevents stuck processes)
                if elapsed < 600:  # 10 minutes
                    print(f"Background context for {title} is currently being generated (started {elapsed:.1f}s ago). Using WebSocket for updates.")
                    return jsonify({
                        'context_pending': True,
                        'message': 'Background context is being generated, use WebSocket for updates.',
                        'cache_key': cache_key,
                        'elapsed_seconds': int(elapsed)
                    })
                else:
                    print(f"Found stale generation process for {title} (started {elapsed:.1f}s ago). Starting fresh generation.")
                    # Fall through to start a new generation

        # Look for similar cache entries for the same title (might have different URL)
        # This helps prevent duplicate generations for the same content
        title_key = generate_cache_key("background", title, topic)
        for existing_key in background_cache.keys():
            if existing_key.startswith(title_key) and existing_key != cache_key:
                existing_entry = background_cache[existing_key]
                # Check if it's complete and has content
                if ('enriched_content' in existing_entry and
                    not existing_entry.get('context_pending', False)):
                    print(f"Found existing content for title '{title}' with different cache key.")
                    print(f"Using existing content instead of generating new context.")

                    # Copy the content to our cache key and return it
                    background_cache[cache_key] = {
                        'enriched_content': existing_entry['enriched_content'],
                        'context_pending': False,
                        'from_cache': True,
                        'timestamp': time.time(),
                        'original_key': existing_key
                    }

                    return jsonify({
                        'enriched_content': existing_entry['enriched_content'],
                        'context_pending': False,
                        'from_cache': True
                    })

        # Initialize an entry in the cache to mark the start of processing
        # This happens while we still hold the lock, preventing race conditions
        background_cache[cache_key] = {
            'start_time': time.time(),
            'context_pending': True,
            'thread_id': None  # Will be set by the thread when it starts
        }

        # Log that we're starting a new thread
        print(f"Starting background thread to generate enriched context for {title}")

    # Start a background thread to generate the enriched context
    # We can release the lock before starting the thread
    fetch_thread = threading.Thread(
        target=generate_enriched_context_async,
        args=(title, topic, summary_json, url, cache_key),
        daemon=True
    )
    fetch_thread.start()

    # Return status that indicates it's being generated
    return jsonify({
        'context_pending': True,
        'complete': False,
        'elapsed_seconds': 0,
        'cache_key': cache_key
    })

def generate_enriched_context_async(title, topic, summary_json, url, cache_key):
    """Generate enriched context using tiered model approach asynchronously and store in cache."""
    # Get thread ID for logging
    thread_id = threading.current_thread().ident

    # Acquire lock to update cache with thread ID
    with background_cache_lock:
        # Double-check that this generation is still needed
        if cache_key in background_cache:
            # Check if another thread has already completed this work
            if 'enriched_content' in background_cache[cache_key] and not background_cache[cache_key].get('context_pending', True):
                print(f"Thread #{thread_id}: Generation already completed for {title}, exiting.")
                return

            # Update the thread ID in the cache
            background_cache[cache_key]['thread_id'] = thread_id

            # Log thread start
            print(f"====== STARTED CONTEXT THREAD #{thread_id} for '{title}' ({cache_key}) ======")

    try:
        start_time = time.time()

        # Emit WebSocket event to notify clients that processing has started
        socketio.emit('context_update', {
            'status': 'started',
            'cache_key': cache_key,
            'message': 'Started generating background context',
            'thread_id': thread_id
        }, namespace='/context')

        # Extract article content to use for context generation
        article_content = ""

        # Priority 1: Try to get content from the article cache
        if url:
            # Generate standardized cache key
            article_cache_key = generate_article_content_key(url)

            # Check if we have this URL's content in our cache
            if article_cache_key and article_cache_key in article_content_cache:
                article_content = article_content_cache[article_cache_key]
                print(f"Using cached article content for URL: {url} ({len(article_content)} characters)")

                # Emit WebSocket event for progress update
                socketio.emit('context_update', {
                    'status': 'progress',
                    'cache_key': cache_key,
                    'message': 'Retrieved article content from cache',
                    'progress': 25
                }, namespace='/context')

        # If no content available, fall back to URL extraction (if URL provided)
        if not article_content and url:
            # Check if this is a YouTube video URL
            is_youtube_video = url and ('youtube.com/watch' in url or 'youtu.be/' in url)

            if is_youtube_video:
                try:
                    print(f"Extracting YouTube video content for context generation: {url}")

                    # Emit WebSocket event for progress update
                    socketio.emit('context_update', {
                        'status': 'progress',
                        'cache_key': cache_key,
                        'message': 'Extracting YouTube video content and transcript',
                        'progress': 15
                    }, namespace='/context')

                    # Extract video ID and get comprehensive content
                    video_id = youtube_service.extract_video_id(url)
                    if video_id:
                        article_content = youtube_service.get_video_content_for_summarization(
                            video_id=video_id,
                            title=title
                        )

                        if "Transcript (Language:" in article_content:
                            print(f"YouTube video content with transcript extracted: {len(article_content)} characters")
                        else:
                            print(f"YouTube video content without transcript extracted: {len(article_content)} characters")

                        # Emit WebSocket event for progress update
                        socketio.emit('context_update', {
                            'status': 'progress',
                            'cache_key': cache_key,
                            'message': 'YouTube content extracted successfully',
                            'progress': 30
                        }, namespace='/context')

                        # Store in article content cache for future use
                        if article_content:
                            article_cache_key = generate_article_content_key(url)
                            if article_cache_key:
                                article_content_cache[article_cache_key] = article_content
                                print(f"Stored YouTube content in cache for URL: {url}")

                except Exception as youtube_error:
                    print(f"Error extracting YouTube content for context: {str(youtube_error)}")

                    # Emit WebSocket event for YouTube extraction error
                    socketio.emit('context_update', {
                        'status': 'progress',
                        'cache_key': cache_key,
                        'message': 'YouTube content extraction encountered an issue, continuing with available data',
                        'progress': 30
                    }, namespace='/context')
            else:
                # Handle regular web articles
                try:
                    print(f"Content not available, extracting article content via URL for context generation: {url}")

                    # Emit WebSocket event for progress update
                    socketio.emit('context_update', {
                        'status': 'progress',
                        'cache_key': cache_key,
                        'message': 'Extracting article content',
                        'progress': 15
                    }, namespace='/context')

                    extraction_result = summarizer.extract_content(url, fetch_image=False)

                    # Handle the case where extraction_result can be either a string or a dictionary
                    if isinstance(extraction_result, dict):
                        article_content = extraction_result.get("content", "")
                    else:
                        # If extraction_result is a string, use it directly as the content
                        article_content = extraction_result if extraction_result else ""

                    print(f"Article content extracted via newspaper3k: {len(article_content)} characters")

                    # Emit WebSocket event for progress update
                    socketio.emit('context_update', {
                        'status': 'progress',
                        'cache_key': cache_key,
                        'message': 'Article content extracted',
                        'progress': 30
                    }, namespace='/context')

                    # Store in article content cache for future use
                    if url and article_content:
                        article_cache_key = generate_article_content_key(url)
                        if article_cache_key:
                            article_content_cache[article_cache_key] = article_content
                            print(f"Stored extracted article content in cache for URL: {url}")
                except Exception as extract_error:
                    print(f"Error extracting content for context: {str(extract_error)}")

                    # Emit WebSocket event for extraction error
                    socketio.emit('context_update', {
                        'status': 'progress',
                        'cache_key': cache_key,
                        'message': 'Content extraction encountered an issue, continuing with available data',
                        'progress': 30
                    }, namespace='/context')

        # Emit WebSocket event for context generation
        socketio.emit('context_update', {
            'status': 'progress',
            'cache_key': cache_key,
            'message': 'Generating enriched context',
            'progress': 50
        }, namespace='/context')

        # Generate the enriched context with tiered model approach
        enriched_content = generate_enriched_context(title, topic, summary_json, article_content)

        # Emit WebSocket event for content formatting
        socketio.emit('context_update', {
            'status': 'progress',
            'cache_key': cache_key,
            'message': 'Formatting content',
            'progress': 85
        }, namespace='/context')

        # CRITICAL FIX: Always ensure the content has a wrapper div with enriched-context class
        # This is necessary for the frontend to properly handle the content
        if enriched_content:
            # Check if it already has the wrapper
            if "<div class=\"enriched-context\"" not in enriched_content and '<div class="enriched-context"' not in enriched_content:
                enriched_content = f"""
                <div class="enriched-context">
                    <h5 class="mb-3"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
                    <div class="context-content">
                        {enriched_content}
                    </div>
                </div>
                """
                print(f"Added wrapper div to enriched content for: {title}")
            else:
                print(f"Content already has enriched-context wrapper")

                # CRITICAL FIX: Verify that the context-content div actually contains content
                # If it's empty, extract the citations and add them back
                empty_content_div_pattern = re.compile(r'<div class="context-content">\s*</div>|<div class="context-content">\s*\n\s*</div>', re.DOTALL)
                if empty_content_div_pattern.search(enriched_content):
                    print(f"WARNING: Found empty context-content div in the enriched content for: {title}")

                    # Extract any citations that might be present
                    citations_match = re.search(r'(<div class=\'citations.*?</div>\s*</div>)', enriched_content, re.DOTALL)
                    citations = citations_match.group(1) if citations_match else ""

                    # Reconstruct the content to ensure the context-content div is not empty
                    enriched_content = f"""
                    <div class="enriched-context">
                        <h5 class="mb-3"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
                        <div class="context-content">
                            <p>We've found some useful sources about this topic, but our background context generator encountered an issue extracting the detailed information. You can check the sources below or try the "Ask Anything" feature for more specific information about this topic.</p>
                        </div>
                        {citations}
                        <div class="text-muted mt-3 small">
                            <i class="bi bi-info-circle me-1"></i>This was generated using AI and real-time web data to enhance your understanding of the news article.
                        </div>
                    </div>
                    """
                    print(f"Fixed empty context-content div for: {title}")

            # Debug log the first part of the content to verify it has the proper structure
            print(f"Content structure ready for: {title}")

        generation_time = time.time() - start_time
        print(f"Enriched context generation time: {generation_time:.2f}s for article: {title}")

        # Store in cache with timestamp
        background_cache[cache_key] = {
            'enriched_content': enriched_content,
            'complete': True,
            'context_pending': False,
            'timestamp': time.time(),
            'generation_time': generation_time
        }

        # Emit WebSocket event for completion
        socketio.emit('context_ready', {
            'cache_key': cache_key,
            'enriched_content': enriched_content,
            'generation_time': generation_time
        }, namespace='/context')

    except Exception as e:
        print(f"Error in generate_enriched_context_async: {e}")
        # Store error state in cache with a more helpful message
        error_content = f"""
        <div class="enriched-context">
            <h5 class="mb-3"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Sorry!</strong> We couldn't retrieve Additional Context for this article.
                <p class="mt-2">This might be due to one of the following reasons:</p>
                <ul>
                    <li>The article content couldn't be accessed properly</li>
                    <li>The article might be behind a paywall</li>
                    <li>There might be a temporary issue with our services</li>
                </ul>
                <p>Try the "Ask Anything" feature to get specific information about this topic.</p>
            </div>
        </div>
        """

        background_cache[cache_key] = {
            'enriched_content': error_content,
            'complete': True,
            'context_pending': False,
            'error': True,
            'timestamp': time.time()
        }

        # Emit WebSocket event for error
        socketio.emit('context_error', {
            'cache_key': cache_key,
            'message': 'An error occurred during context generation',
            'content': error_content
        }, namespace='/context')

def generate_enriched_context(title, topic, summary_json, article_content=""):
    """
    Generate enriched context for a news article using OpenAI's web search.
    Uses article data with the following priority:
    1. Article content from cache or direct URL extraction
    2. Summary information from the article
    """
    try:
        print(f"Starting enriched context generation for: {title}")

        # Parse the summary JSON - Note: We no longer expect API content in this JSON
        # It will only contain summary and key_points
        try:
            # Attempt to parse the JSON string
            summary_data = json.loads(summary_json)
        except (json.JSONDecodeError, TypeError):
            # If parsing fails or if summary_json is not a string, use default values
            summary_data = {"summary": "", "key_points": []}
            print(f"Error parsing summary JSON for {title}, using default values")

        # Get summary and key points, with fallbacks
        article_summary = summary_data.get('summary', '') or summary_data.get('article_summary', '')
        key_points = summary_data.get('key_points', [])

        # Ensure key_points is a list
        if not isinstance(key_points, list):
            key_points = []

        # Format key points for the prompt
        key_points_text = "\n".join([f"- {point}" for point in key_points])

        # Create a prompt for OpenAI with web search
        system_message = """You are a knowledgeable assistant providing accurate answers based on web search results. Your goal is to deliver
        current and reliable information in a natural, structured way while offering useful background and context for news articles.

=========== CRITICAL OUTPUT REQUIREMENTS ===========
1. WRITE IN ENGLISH ONLY - never include content in other languages
2. NEVER end your response with lists, bullet points, or headlines
3. ALWAYS end with a full concluding paragraph with complete sentences
4. ALL paragraphs must be complete with proper punctuation
5. NO LIST-LIKE CONTENT or numbered items anywhere in your response
6. NO BULLET POINTS, dashes (-), or asterisks (*) at the beginning of lines
7. NO SQUARE BRACKETS or any special formatting
8. ABSOLUTELY NO line items that resemble news headlines at the end
============================================

FORMATTING GUIDELINES:
1. Use a conversational, natural tone - respond as if you're speaking directly to the user
2. Use proper HTML paragraph tags (<p>) for basic structure only
3. Keep your answer concise and to the point
4. Use simple emphasis with <em> or <strong> only when truly needed
5. Ensure all parentheses are properly paired
6. Use standard English punctuation only - no special characters
7. ORGANIZE CONTENT INTO SECTIONS when appropriate using <h6> tags for section headers
8. Each section should have a short, descriptive title that summarizes its content
9. Create 2-4 sections if the topic warrants it (e.g. Historical Background, Current Developments, Key Players, Implications)

CONTENT STRUCTURE:
1. Start with an introductory paragraph that directly addresses the main topic
2. Organize subsequent information into logical sections with relevant <h6> headers
3. Within each section, present details in well-formed paragraphs
4. End with a proper concluding paragraph that summarizes the key points
5. NEVER end with dash-prefixed items, headlines, or incomplete sentences

PROHIBITED CONTENT:
1. NO website names, domains, or URLs in your answer
2. NO citation markers or references - never include text like "(Source: X)" or "(via website)"
3. NO titles of articles or headlines in your response
4. NO bullet points, dashes, or any list-like formatting
5. NO markdown or special formatting beyond basic HTML paragraphs and <h6> headers
6. NO tracking links, URL fragments, or domain references
7. NO stray punctuation, brackets, or unmatched characters
8. NO non-English content whatsoever

CITATIONS:
- All sources will be automatically formatted as clickable links at the end of your response
- Simply focus on providing a direct, conversational answer without any source references
- DO NOT include any citations or URL fragments directly in your text"""

        # Build prompt parts
        prompt_parts = [
            "Generate comprehensive background context for this news article that combines the article content with current information from the web:",
            "",
            f"Title: {title}",
            f"Topic: {topic}",
            "",
        ]

        # Include full article content if available
        if article_content and len(article_content) > 200:
            prompt_parts.extend([
                f"Full Article Content:",
                f"{article_content[:3500]}",  # Limit to 3500 characters to fit within token limits
                "",
                f"Article Summary",
                f"{article_summary}",
                ""
            ])
        else:
            prompt_parts.extend([
                f"Article Summary",
                f"{article_summary}",
                ""
            ])

        if key_points:
            prompt_parts.append("Key Points")
            prompt_parts.append(key_points_text)
            prompt_parts.append("")

        prompt_parts.extend([
            "Please provide detailed contextual information (300-500 words) that will:",
            "1) Explain the background and context of this news story using both the article content and up-to-date web information",
            "2) Add historical context, relevant concepts, and related developments that help understand the significance",
            "3) Include factual information from reliable sources",
            "",
            "Focus on being educational, factual, and helpful to readers who want to understand this news in context."
        ])

        # Join all parts with newlines
        prompt = "\n".join(filter(None, prompt_parts))

        # Call OpenAI with web search enabled
        start_time = time.time()

        # Debug logging
        print(f"Calling OpenAI with web search for context: {title[:50]}...")

        try:
            # Use the search-enabled model
            response = llm_client.chat.completions.create(
                model="gpt-4o-search-preview",  # Use the model with web search capability
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1024,
                web_search_options={}  # Empty object enables web search
            )
            print(f"OpenAI web search used successfully for: {title[:50]}...")

            duration = time.time() - start_time
            print(f"Web search-enhanced context generation took {duration:.2f}s")

            # Extract content and citations from the response
            # For Chat Completions API, use choices[0].message.content
            enriched_content = response.choices[0].message.content

            # Check if we got an actual response
            if not enriched_content or len(enriched_content.strip()) < 50:
                print(f"WARNING: Received very short or empty content from OpenAI: {enriched_content}")

        except Exception as api_error:
            # Detailed error logging
            print(f"ERROR calling OpenAI API for context: {api_error}")
            import traceback
            print(traceback.format_exc())

            # Create a fallback message
            return f"""
            <div class="enriched-context">
                <h5 class="mb-3"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>API Error:</strong> Unable to generate enriched context for this article due to an API issue. Please try again later.
                    <p class="mt-2">Error details: {str(api_error)[:100]}</p>
                </div>
            </div>
            """

        # Clean up any potential markdown formatting that might still appear
        enriched_content = re.sub(r'##\s+', '', enriched_content)  # Remove markdown headers completely
        enriched_content = re.sub(r'\*\*([^*]+)\*\*', r'<strong>\1</strong>', enriched_content)
        enriched_content = re.sub(r'\n\s*-\s+', '\n<li>', enriched_content)
        enriched_content = re.sub(r'\n\s*\*\s+', '\n<li>', enriched_content)

        # More aggressive URL and domain reference removal - enhanced to catch more patterns
        # Step 1: Remove standard inline parenthesized URLs and domain references
        enriched_content = re.sub(r'\s*\([a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+\)', '', enriched_content)
        enriched_content = re.sub(r'\s*\([a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+/[^\)]*\)', '', enriched_content)

        # Step 2: Remove domain names with various TLDs that appear in parentheses
        enriched_content = re.sub(r'\s*\((?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk|de|fr|it|es|ru|cn|jp|br|in|au|ca|eu|info|biz)[^()]*?\)', '', enriched_content)

        # Step 3: Remove any text that looks like a URL or domain reference - with or without parentheses
        enriched_content = re.sub(r'(?<![/a-zA-Z])(?:https?://)?(?:www\.)?(?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk|de|fr)[^\s,.;:!?)\'"]*', '', enriched_content)

        # Step 4: Remove markdown-style links [text](url)
        enriched_content = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'\1', enriched_content)

        # Step 5: Remove any source titles or lists at the end of the text (expanded to catch more patterns)
        enriched_content = re.sub(r'(?:References|Sources|Links|Citations|According to|From):\s*(?:\n|.)*$', '', enriched_content, flags=re.IGNORECASE)

        # Step 6: Remove any tracking parameters or URL fragments
        enriched_content = re.sub(r'\([:/]+[^\)]*(?:utm_|https?:|www\.|\.com)[^\)]*\)', '', enriched_content)
        enriched_content = re.sub(r'\[[^\]]*\]\([^\)]*(?:utm_|https?:|www\.|\.com)[^\)]*\)', '', enriched_content)

        # Step 7: Remove any remaining URL-like patterns in parentheses
        enriched_content = re.sub(r'\([:/]+[^\)]*\)', '', enriched_content)

        # Step 8: Use a second-pass format checker to identify and remove any remaining domain references
        def format_checker(text):
            """Apply a second pass to clean any remaining domain references"""
            # Find text patterns that look like "(domain.tld)" where domain could be any name and tld any common extension
            domain_pattern = re.compile(r'\([^()]*?(?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk|de|fr|it|es|ru|cn|jp|br|in|au|ca|eu|info|biz)[^()]*?\)')

            # Match each domain reference and remove it
            matches = domain_pattern.findall(text)
            for match in matches:
                text = text.replace(match, '')

            # Remove standalone domain references without parentheses
            standalone_domain = re.compile(r'(?<!\w)(?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk)\b')
            text = standalone_domain.sub('', text)

            # Remove UTM parameters and similar tracking fragments
            utm_pattern = re.compile(r'\([^()]*?(?:utm_source|utm_medium|utm_campaign|utm_content|utm_term)[^()]*?\)')
            matches = utm_pattern.findall(text)
            for match in matches:
                text = text.replace(match, '')

            # Remove any remaining URL-like fragments
            url_fragments = re.compile(r'\([^()]*?(?:://|www\.|https?:|\.com)[^()]*?\)')
            matches = url_fragments.findall(text)
            for match in matches:
                text = text.replace(match, '')

            return text

        # Apply the format checker as a second pass
        enriched_content = format_checker(enriched_content)

        # Additional cleanup for stray parentheses, brackets, and other symbols
        # Step 9: Remove any unpaired or stray parentheses
        enriched_content = re.sub(r'(?<!\()(\))', '', enriched_content)  # Remove stray closing parenthesis
        enriched_content = re.sub(r'(\()(?!\))', '', enriched_content)  # Remove stray opening parenthesis

        # Step 10: Remove any isolated special characters or strange symbols that might appear
        enriched_content = re.sub(r'\s+[.,:;/\\\[\]{}|+=_<>~`^&%$#@!-]{1,3}\s+', ' ', enriched_content)  # Isolated special chars

        # Step 11: Fix double spaces that might have been created by the cleanup
        enriched_content = re.sub(r'\s{2,}', ' ', enriched_content)

        # Step 12: Fix sentence fragments that might have been created (ending with prepositions or conjunctions)
        enriched_content = re.sub(r'(?<=[.!?])\s+(?:[Aa]nd|[Oo]r|[Bb]ut|[Aa]s|[Tt]o|[Ww]ith|[Ff]or|[Ii]n|[Oo]n|[Aa]t|[Aa]bout|[Ff]rom|[Aa]fter|[Bb]efore|[Ww]hile)\s+(?=[A-Z])', '. ', enriched_content)

        # IMPROVED BULLET POINT FORMATTING: Convert text bullet points to proper HTML lists
        # Look for patterns like "• Item 1<br>• Item 2" or "- Item 1<br>- Item 2"
        bullet_pattern = re.compile(r'(?:<br\s*/?>\s*|^|\n\s*)([•\-\*⦁◦‣⁃▪▹>]\s*[^<>\n]+?)(?=<br\s*/?>\s*[•\-\*⦁◦‣⁃▪▹>]|</p>|$)', re.MULTILINE)

        # Find sequences of bullet points and convert them to proper HTML lists
        def convert_bullets_to_list(match_obj):
            content = match_obj.group(0)
            # Split by bullet markers and clean up
            items = re.split(r'(?:<br\s*/?>\s*|^|\n\s*)[•\-\*⦁◦‣⁃▪▹>]\s*', content)
            items = [item.strip() for item in items if item.strip()]

            if len(items) >= 2:  # Only convert if we have multiple items
                list_html = '<ul>'
                for item in items:
                    # Clean up any remaining HTML tags in the item
                    clean_item = re.sub(r'</?(?:br|p)\s*/?>', '', item).strip()
                    if clean_item:
                        list_html += f'<li>{clean_item}</li>'
                list_html += '</ul>'
                return list_html
            else:
                # If only one item, just return it as regular text
                return items[0] if items else ''

        # Apply the conversion
        if re.search(bullet_pattern, enriched_content):
            enriched_content = re.sub(bullet_pattern, convert_bullets_to_list, enriched_content)

        # Replace consecutive list items with proper HTML list (existing logic, but improved)
        if '<li>' in enriched_content and '</li>' not in enriched_content:
            enriched_content = enriched_content.replace('<li>', '</p><ul><li>') + '</li></ul><p>'

        # Ensure paragraphs are properly wrapped
        if not enriched_content.startswith('<p>'):
            enriched_content = '<p>' + enriched_content
        if not enriched_content.endswith('</p>'):
            enriched_content = enriched_content + '</p>'

        # Fix multiple paragraph tags that might occur
        enriched_content = re.sub(r'</p>\s*<p>', '</p>\n<p>', enriched_content)

        # Clean up any malformed list structures
        enriched_content = re.sub(r'<p>\s*<ul>', '<ul>', enriched_content)
        enriched_content = re.sub(r'</ul>\s*</p>', '</ul>', enriched_content)
        enriched_content = re.sub(r'<p>\s*</p>', '', enriched_content)  # Remove empty paragraphs

        # Process any annotations/citations that might be in the response
        # For Chat Completions API, get annotations from choices[0].message
        annotations = getattr(response.choices[0].message, 'annotations', []) or []
        citation_html = ""

        # Create a well-formatted sources section
        if annotations:
            citation_html = """
            <div class='citations mt-4'>
                <hr>
                <h6 class='mb-2'><strong>Sources:</strong></h6>
                <ol class='small ps-3'>
            """
            # Create a set to track unique URLs and avoid duplicates
            seen_urls = set()
            for annotation in annotations:
                if annotation.type == 'url_citation':
                    url = annotation.url_citation.url
                    title = annotation.url_citation.title or 'Source'
                    domain = url.split('/')[2] if '/' in url else url  # Extract domain from URL

                    # Only add if this URL hasn't been seen before
                    if url and url not in seen_urls:
                        seen_urls.add(url)
                        citation_html += f"<li><a href='{url}' target='_blank'>{title}</a> <span class='text-muted'>({domain})</span></li>"
            citation_html += "</ol></div>"

        # Add a header and footer to the enriched content
        formatted_content = f"""
        <div class="enriched-context">
            <h5 class="mb-3"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
            <div class="context-content">
                {enriched_content}
            </div>
            {citation_html}
            <div class="text-muted mt-3 small">
                <i class="bi bi-info-circle me-1"></i>This was generated using AI and real-time web data to enhance your understanding of the news article.
            </div>
        </div>
        """

        # Final validation - remove any section that might look like article titles or non-English content
        formatted_content = re.sub(r'<p>[^<>]*?(?:[A-Z][^.!?<>:]*:|\b(?:at|in) \w+)(?:\s*<br\s*/?>\s*|\s*\n\s*)[A-Z][^.!?<>]*</p>\s*$', '', formatted_content)

        # Final check for non-English characters (outside ASCII) that might have slipped through
        if re.search(r'[^\x00-\x7F]', formatted_content):
            # Remove the paragraph containing non-English text
            formatted_content = re.sub(r'<p>[^<>]*[^\x00-\x7F][^<>]*</p>', '', formatted_content)

        # Additional check for specific Spanish patterns and common Spanish words
        spanish_patterns = r'\b(?:el|la|los|las|una|uno|unos|unas|en|con|por|para|como|pero|más|está|están|es|son|ser|estar|hace|hizo|dice|dijo|según|puede|pueden|podría|podrían)\b'
        if re.search(spanish_patterns, formatted_content, re.IGNORECASE):
            # If Spanish text is detected, remove the entire paragraph
            formatted_content = re.sub(r'<p>[^<>]*' + spanish_patterns + r'[^<>]*</p>', '', formatted_content, flags=re.IGNORECASE)

        # Check for square brackets in the final content and remove any paragraph containing them
        if '[' in formatted_content or ']' in formatted_content:
            formatted_content = re.sub(r'<p>[^<>]*[\[\]][^<>]*</p>', '', formatted_content)

        # IMPROVED: Only remove problematic dash-prefixed items, not all bullet points
        # Remove only incomplete or malformed dash items that don't end with proper punctuation
        dash_prefixed_item = re.compile(r'(?:<br\s*/?>\s*|\n\s*)[-–—]\s*[^<>\n.!?]*?(?=<br|</p>|$)(?![.!?])')
        if dash_prefixed_item.search(formatted_content):
            formatted_content = re.sub(dash_prefixed_item, '', formatted_content)

        # IMPROVED: More targeted pattern for problematic headline bullet combinations
        # Only remove if it's a title followed by incomplete bullet points (no proper punctuation)
        headline_bullet_pattern = re.compile(r'<p>[^<>]*:[^<>]*?(?:<br\s*/?>\s*|\n\s*)[-•*⦁◦‣⁃▪▹>\[\]]+\s*[^.!?<>]*?(?:<br\s*/?>\s*|\n\s*[-•*⦁◦‣⁃▪▹>\[\]]+[^.!?<>]*?)*(?!</li>)(?![.!?])</p>', re.DOTALL)
        if headline_bullet_pattern.search(formatted_content):
            # Remove the entire paragraph containing this pattern
            formatted_content = re.sub(headline_bullet_pattern, '', formatted_content)

            # If we removed something, make sure we still have a proper conclusion
            if not re.search(r'<p>[^<>]{40,}[.!?]</p>', formatted_content):
                # Add a generic conclusion if we don't have one
                conclusion_text = "<p>Understanding these developments provides essential context for interpreting current events and their potential implications for the future.</p>"
                # Insert before the end of the context-content div
                formatted_content = re.sub(r'(</div>\s*' + re.escape(citation_html) + ')', conclusion_text + '</div>' + citation_html, formatted_content)

        # Check for empty sections and remove them (h6 headers followed immediately by another h6 or with no content)
        empty_section_pattern = re.compile(r'<h6[^>]*>[^<>]*</h6>\s*(?=<h6|</div>)', re.DOTALL)
        while empty_section_pattern.search(formatted_content):
            formatted_content = re.sub(empty_section_pattern, '', formatted_content)

        # Check for sections with minimal content (less than 40 characters)
        minimal_section_pattern = re.compile(r'(<h6[^>]*>[^<>]*</h6>\s*)(<p>[^<>]{1,40}</p>\s*)(?=<h6|</div>)', re.DOTALL)
        while minimal_section_pattern.search(formatted_content):
            formatted_content = re.sub(minimal_section_pattern, '', formatted_content)

        # Fix double periods in the enriched content (similar to fix in ask_anything)
        formatted_content = re.sub(r'([.!?])\s*\.', r'\1', formatted_content)
        formatted_content = re.sub(r'\.{2,}', '.', formatted_content)

        return formatted_content

    except Exception as e:
        print(f"Error generating enriched context: {e}")
        return f"""
        <div class="enriched-context">
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Unable to generate enriched context for this article. Please try again later.
            </div>
        </div>
        """

# Add WebSocket route for context updates
@socketio.on('connect', namespace='/context')
def handle_connect():
    print('Client connected to context namespace')

@socketio.on('disconnect', namespace='/context')
def handle_disconnect():
    print('Client disconnected from context namespace')

@socketio.on('subscribe', namespace='/context')
def handle_subscribe(data):
    cache_key = data.get('cache_key')
    if not cache_key:
        return

    print(f"Client subscribed to updates for cache_key: {cache_key}")

    # Check if content is already available
    if cache_key in background_cache:
        cache_entry = background_cache[cache_key]

        # If content is already generated, emit it immediately
        if 'enriched_content' in cache_entry and not cache_entry.get('context_pending', False):
            print(f"Emitting cached content immediately for cache_key: {cache_key}")
            socketio.emit('context_ready', {
                'cache_key': cache_key,
                'enriched_content': cache_entry['enriched_content'],
                'generation_time': cache_entry.get('generation_time', 0),
                'from_cache': True
            }, namespace='/context')
        elif cache_entry.get('context_pending', False):
            # If content is still being generated, send a status update
            start_time = cache_entry.get('start_time', time.time())
            elapsed = time.time() - start_time
            socketio.emit('context_update', {
                'status': 'progress',
                'cache_key': cache_key,
                'message': 'Context generation is in progress',
                'progress': 50,
                'elapsed_seconds': int(elapsed)
            }, namespace='/context')

@main.route('/ask_anything', methods=['GET', 'POST'])
def ask_anything():
    """Ask AI a question about the article or any general topic using OpenAI's web search."""
    try:
        # Get parameters based on request method
        if request.method == 'POST':
            data = request.json
            title = data.get('title', '')
            question = data.get('question', '')
            topic = data.get('topic', '')
            url = data.get('url', '')
        else:
            title = request.args.get('title', '')
            question = request.args.get('question', '')
            topic = request.args.get('topic', '')
            url = request.args.get('url', '')

        # No need to initialize api_content as we'll use the cache

        if not question:
            return jsonify({'error': 'Missing question'}), 400

        # Validate topic if provided
        if topic and topic not in ALLOWED_TOPICS:
            print(f"WARNING: Invalid topic in ask_anything request: {topic}. Proceeding with empty topic.")
            topic = ''  # Set to empty string instead of using invalid topic

        # Get article content with priority:
        # 1. Check article content cache
        # 2. Extract using summarizer (which uses newspaper3k)
        article_content = ""

        # Debug: Print URL to verify it's being passed correctly
        print(f"Ask Anything - Processing URL: '{url}'")

        # If URL is provided, try to get from cache or extract
        if url:
            # Generate standardized cache key
            article_cache_key = generate_article_content_key(url)
            print(f"Ask Anything - Generated cache key: '{article_cache_key}'")

            # Check if we have this URL's content in our cache
            if article_cache_key and article_cache_key in article_content_cache:
                article_content = article_content_cache[article_cache_key]
                print(f"Using cached article content for URL: {url} ({len(article_content)} characters)")
            else:
                # No cached content, extract it
                try:
                    # Use the summarizer's extract_content method which already uses newspaper3k
                    print(f"Ask Anything - Extracting content for URL: {url}")
                    extraction_result = summarizer.extract_content(url)
                    if isinstance(extraction_result, dict):
                        article_content = extraction_result.get("content", "")
                    else:
                        article_content = extraction_result
                    print(f"Extracted article content: {len(article_content)} characters")

                    # Cache the extracted content for future use
                    if article_content and article_cache_key:
                        article_content_cache[article_cache_key] = article_content
                        print(f"Cached article content for URL: {url}")
                except Exception as e:
                    print(f"Error extracting article content: {e}")

        # Create a unique cache key for this question+title+content combination
        cache_key = generate_cache_key("ask_anything", title, question, article_content[:100] if article_content else "")

        # Periodically clean up the cache - use central cleanup function
        cleanup_cache()

        # Check if we have a cached answer
        if cache_key in background_cache:
            print(f"Cache hit for question: {question[:30]}...")
            # Update timestamp to mark as recently accessed
            background_cache[cache_key]['timestamp'] = time.time()
            return jsonify({'answer': background_cache[cache_key]['answer']})

        # Generate answer using OpenAI with web search
        print(f"Generating answer with web search for: {question[:50]}...")

        # Prepare the system message
        system_message = """You are a knowledgeable assistant providing accurate, concise, and conversational answers based on the article content or web search results, depending on the query's relevance to the article.
Your goal is to deliver the most current and reliable information in a natural, direct way - as if you're having a conversation with the user.

=========== CRITICAL OUTPUT REQUIREMENTS ===========
1. WRITE IN ENGLISH ONLY - never include content in other languages
2. NEVER end your response with lists, bullet points, or headlines
3. ALWAYS end with a full concluding paragraph with complete sentences
4. ALL paragraphs must be complete with proper punctuation
5. NO LIST-LIKE CONTENT or numbered items anywhere in your response
6. NO BULLET POINTS, dashes (-), or asterisks (*) at the beginning of lines
7. NO SQUARE BRACKETS or any special formatting
8. ABSOLUTELY NO line items that resemble news headlines at the end
============================================

FORMATTING GUIDELINES:
1. Use a conversational, natural tone - respond as if you're speaking directly to the user
2. Use proper HTML paragraph tags (<p>) for basic structure only
3. Keep your answer concise and to the point - aim for 150-250 words
4. Use simple emphasis with <em> or <strong> only when truly needed
5. Write as if you're answering a friend's question - warm and accessible
6. Ensure all parentheses are properly paired
7. Use standard English punctuation only - no special characters

CONTENT STRUCTURE:
1. Start with a direct answer to the question
2. Present supporting details and explanations in 2-3 well-formed paragraphs
3. End with a proper concluding paragraph that summarizes the key points
4. Make sure your final paragraph wraps up the information naturally
5. NEVER end with dash-prefixed items, headlines, or incomplete sentences

PROHIBITED CONTENT:
1. NO website names, domains, or URLs in your answer
2. NO citation markers or references - never include text like "(Source: X)" or "(via website)"
3. NO titles of articles or headlines in your response
4. NO bullet points, dashes, or any list-like formatting
5. NO markdown or special formatting beyond basic HTML paragraphs
6. NO tracking links, URL fragments, or domain references
7. NO stray punctuation, brackets, or unmatched characters
8. NO non-English content whatsoever

CITATIONS:
- All sources will be automatically formatted as clickable links at the end of your response
- Simply focus on providing a direct, conversational answer without any source references
- DO NOT include any citations or URL fragments directly in your text"""

        # Create the prompt
        # Prepare the article content section separately to avoid backslash issues in f-strings
        article_content_section = ""
        if article_content:
            article_content_section = f"\n\nArticle Content:\n{article_content[:4000]}"

        prompt = f"""Please answer this question conversationally using relevant information from the article content or web search, depending on the query's relevance to the article:

Question: {question}

{f"Context - Article Title: {title}" if title else ""}
{f"Context - Topic: {topic}" if topic else ""}
{article_content_section}

Please provide a direct answer that:
1) Addresses the question as if speaking directly to the user
2) Is concise (150-250 words) and easy to read
3) Avoids unnecessary jargon or complexity
4) Feels like a natural human response
5) Integrates factual information smoothly

Remember to stay focused on answering the question directly. The system will automatically handle formatting and sources - you just need to provide helpful, factual information in a conversational style."""

        # Call OpenAI with web search enabled
        print(f"Calling OpenAI Chat Completions API with article content length: {len(article_content) if article_content else 0}")
        # Track how long the API call takes
        api_call_start = time.time()

        # This is the critical part - wrap the OpenAI call and response processing separately
        try:
            response = llm_client.chat.completions.create(
                model="gpt-4o-search-preview",  # Use the model with web search capability
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1024,
                web_search_options={}  # Empty object enables web search
            )

            # Extract the answer
            answer = response.choices[0].message.content

            # Log how long the API call took
            api_call_duration = time.time() - api_call_start
            print(f"OpenAI API call completed in {api_call_duration:.2f} seconds")

        except Exception as openai_error:
            print(f"OpenAI API call failed: {str(openai_error)}")
            # Return a user-friendly error for OpenAI failures
            error_answer = f"""
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">
                            <i class="bi bi-question-circle-fill me-2 text-primary"></i>
                            {question}
                        </h6>
                        <div class="ai-answer mt-3">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Unable to process your question at the moment. Please try again.
                            </div>
                        </div>
                    </div>
                </div>
            """
            return jsonify({'answer': error_answer}), 200  # Return 200 to avoid frontend error handling

        # Now process the successful response - wrap this in a try-catch to handle formatting errors
        try:
            # Clean up any potential markdown formatting that might still appear
            answer = re.sub(r'##\s+', '', answer)  # Remove markdown headers completely
            answer = re.sub(r'\*\*([^*]+)\*\*', r'<strong>\1</strong>', answer)
            answer = re.sub(r'\n\s*-\s+', '\n<li>', answer)
            answer = re.sub(r'\n\s*\*\s+', '\n<li>', answer)

            # More aggressive URL and domain reference removal - enhanced to catch more patterns
            # Step 1: Remove standard inline parenthesized URLs and domain references
            answer = re.sub(r'\s*\([a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+\)', '', answer)
            answer = re.sub(r'\s*\([a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+/[^\)]*\)', '', answer)

            # Step 2: Remove domain names with various TLDs that appear in parentheses
            answer = re.sub(r'\s*\((?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk|de|fr|it|es|ru|cn|jp|br|in|au|ca|eu|info|biz)[^()]*?\)', '', answer)

            # Step 3: Remove any text that looks like a URL or domain reference - with or without parentheses
            answer = re.sub(r'(?<![/a-zA-Z])(?:https?://)?(?:www\.)?(?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk|de|fr)[^\s,.;:!?)\'"]*', '', answer)

            # Step 4: Remove markdown-style links [text](url)
            answer = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'\1', answer)

            # Step 5: Remove any source titles or lists at the end of the text (expanded to catch more patterns)
            answer = re.sub(r'(?:References|Sources|Links|Citations|According to|From):\s*(?:\n|.)*$', '', answer, flags=re.IGNORECASE)

            # Step 6: Remove any tracking parameters or URL fragments
            answer = re.sub(r'\([:/]+[^\)]*(?:utm_|https?:|www\.|\.com)[^\)]*\)', '', answer)
            answer = re.sub(r'\[[^\]]*\]\([^\)]*(?:utm_|https?:|www\.|\.com)[^\)]*\)', '', answer)

            # Step 7: Remove any remaining URL-like patterns in parentheses
            answer = re.sub(r'\([:/]+[^\)]*\)', '', answer)

            # Step 8: Use a second-pass format checker to identify and remove any remaining domain references
            def format_checker(text):
                """Apply a second pass to clean any remaining domain references"""
                # Find text patterns that look like "(domain.tld)" where domain could be any name and tld any common extension
                domain_pattern = re.compile(r'\([^()]*?(?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk|de|fr|it|es|ru|cn|jp|br|in|au|ca|eu|info|biz)[^()]*?\)')

                # Match each domain reference and remove it
                matches = domain_pattern.findall(text)
                for match in matches:
                    text = text.replace(match, '')

                # Remove standalone domain references without parentheses
                standalone_domain = re.compile(r'(?<!\w)(?:[a-zA-Z0-9-]+\.)+(?:com|org|net|gov|edu|co|io|uk)\b')
                text = standalone_domain.sub('', text)

                # Remove UTM parameters and similar tracking fragments
                utm_pattern = re.compile(r'\([^()]*?(?:utm_source|utm_medium|utm_campaign|utm_content|utm_term)[^()]*?\)')
                matches = utm_pattern.findall(text)
                for match in matches:
                    text = text.replace(match, '')

                # Remove any remaining URL-like fragments
                url_fragments = re.compile(r'\([^()]*?(?:://|www\.|https?:|\.com)[^()]*?\)')
                matches = url_fragments.findall(text)
                for match in matches:
                    text = text.replace(match, '')

                return text

            # Apply the format checker as a second pass
            answer = format_checker(answer)

            # Additional cleanup for stray parentheses, brackets, and other symbols
            # Step 9: Remove any unpaired or stray parentheses
            answer = re.sub(r'(?<!\()(\))', '', answer)  # Remove stray closing parenthesis
            answer = re.sub(r'(\()(?!\))', '', answer)  # Remove stray opening parenthesis

            # Step 10: Remove any isolated special characters or strange symbols that might appear
            answer = re.sub(r'\s+[.,:;/\\\[\]{}|+=_<>~`^&%$#@!-]{1,3}\s+', ' ', answer)  # Isolated special chars

            # Step 11: Fix double spaces that might have been created by the cleanup
            answer = re.sub(r'\s{2,}', ' ', answer)

            # Step 12: Fix sentence fragments that might have been created (ending with prepositions or conjunctions)
            answer = re.sub(r'(?<=[.!?])\s+(?:[Aa]nd|[Oo]r|[Bb]ut|[Aa]s|[Tt]o|[Ww]ith|[Ff]or|[Ii]n|[Oo]n|[Aa]t|[Aa]bout|[Ff]rom|[Aa]fter|[Bb]efore|[Ww]hile)\s+(?=[A-Z])', '. ', answer)

            # Replace consecutive list items with proper HTML list
            if '<li>' in answer and '</li>' not in answer:
                answer = answer.replace('<li>', '</p><ul><li>') + '</li></ul><p>'

            # Ensure paragraphs are properly wrapped
            if not answer.startswith('<p>'):
                answer = '<p>' + answer
            if not answer.endswith('</p>'):
                answer = answer + '</p>'

            # Fix multiple paragraph tags that might occur
            answer = re.sub(r'</p>\s*<p>', '</p>\n<p>', answer)

            # Process any annotations/citations
            # For Chat Completions API, get annotations from message
            annotations = getattr(response.choices[0].message, 'annotations', []) or []
            citation_html = ""

            # If we have annotations, create a sources section with deduplication
            if annotations:
                # Create a set to track unique URLs and avoid duplicates
                seen_urls = set()
                citation_html = "<div class='sources mt-3 small'><hr><p><strong>Sources:</strong></p><ol class='ps-3'>"

                for annotation in annotations:
                    if annotation.type == 'url_citation':
                        url = annotation.url_citation.url
                        title = annotation.url_citation.title or 'Source'
                        domain = url.split('/')[2] if '/' in url else url

                        # Only add if this URL hasn't been seen before
                        if url and url not in seen_urls:
                            seen_urls.add(url)
                            citation_html += f"<li><a href='{url}' target='_blank'>{title}</a> <span class='text-muted'>({domain})</span></li>"

                citation_html += "</ol></div>"

            # Additional comprehensive cleaning for list-like content and improper endings
            # Step 1: Remove any non-English text and article title formats
            # Find and remove content that looks like article titles or news headlines (typically sentence case, no ending punctuation)
            answer = re.sub(r'(?:\n|<\/p>)(?:[A-Z][^.!?:]*(?::|$)|(?:[A-Z][a-z]+\s+){2,}[^.!?]*(?:\n|$))\s*$', '', answer)

            # Step 2: Remove any content with Spanish/Latin characters and accents
            latin_chars_pattern = re.compile(r'[áéíóúüñÁÉÍÓÚÜÑàèìòùÀÈÌÒÙâêîôûÂÊÎÔÛëïÿËÏŸçÇ]')
            paragraphs = re.split(r'(</?p>)', answer)
            cleaned_paragraphs = []
            for i in range(0, len(paragraphs), 2):
                if i+1 < len(paragraphs):
                    p_content = paragraphs[i]
                    p_tag = paragraphs[i+1] if i+1 < len(paragraphs) else ""

                    # If paragraph contains Latin characters, remove it
                    if latin_chars_pattern.search(p_content):
                        continue
                    cleaned_paragraphs.append(p_content + p_tag)

            # Reassemble the content if we removed paragraphs
            if cleaned_paragraphs:
                answer = ''.join(cleaned_paragraphs)

            # Step 3: Remove any trailing bullet-like items or incomplete sentences at the end
            answer = re.sub(r'(?:\n|<\/p>)[-•*⦁◦‣⁃▪▹>]\s*[^.!?]*$', '', answer)  # Bullet points
            answer = re.sub(r'(?:\n|<\/p>)[A-Z][^.!?]*$', '', answer)  # Incomplete sentences

            # IMPROVED BULLET POINT HANDLING: Convert text bullet points to proper HTML lists
            # Look for patterns like "• Item 1<br>• Item 2" or "- Item 1<br>- Item 2"
            bullet_pattern = re.compile(r'(?:<br\s*/?>\s*|^|\n\s*)([•\-\*⦁◦‣⁃▪▹>]\s*[^<>\n]+?)(?=<br\s*/?>\s*[•\-\*⦁◦‣⁃▪▹>]|</p>|$)', re.MULTILINE)

            # Find sequences of bullet points and convert them to proper HTML lists
            def convert_bullets_to_list(match_obj):
                content = match_obj.group(0)
                # Split by bullet markers and clean up
                items = re.split(r'(?:<br\s*/?>\s*|^|\n\s*)[•\-\*⦁◦‣⁃▪▹>]\s*', content)
                items = [item.strip() for item in items if item.strip()]

                if len(items) >= 2:  # Only convert if we have multiple items
                    list_html = '<ul>'
                    for item in items:
                        # Clean up any remaining HTML tags in the item
                        clean_item = re.sub(r'</?(?:br|p)\s*/?>', '', item).strip()
                        if clean_item:
                            list_html += f'<li>{clean_item}</li>'
                    list_html += '</ul>'
                    return list_html
                else:
                    # If only one item, just return it as regular text
                    return items[0] if items else ''

            # Apply the conversion for well-formed bullet lists
            if re.search(bullet_pattern, answer):
                answer = re.sub(bullet_pattern, convert_bullets_to_list, answer)

            # Step 4: IMPROVED - Only remove problematic title + bullet combinations
            # Only target titles followed by incomplete bullet points (no proper punctuation)
            title_with_bullets_pattern = re.compile(r'<p>[^<>]*:[^<>]*?(?:<br\s*/?>\s*|\n\s*)[-•*⦁◦‣⁃▪▹>][^.!?<>]*?(?!</li>)(?![.!?])</p>$', re.DOTALL)
            if title_with_bullets_pattern.search(answer):
                # If such a pattern exists, remove the entire paragraph containing it
                answer = re.sub(r'<p>[^<>]*:[^<>]*?(?:<br\s*/?>\s*|\n\s*)[-•*⦁◦‣⁃▪▹>][^.!?<>]*?(?!</li>)(?![.!?])</p>$', '', answer, flags=re.DOTALL)

            # Step 5: Process specific case from user's example with bullet points after a paragraph
            conflict_pattern = re.compile(r'([.!?])\s+(?:Renewed|New|Latest|Recent|Update)[^.!?<>]*:\s*(?:<br>|\n)*\s*[-•*⦁◦‣⁃▪▹>]', re.IGNORECASE)
            if conflict_pattern.search(answer):
                # Cut off the text at the period before the problematic section
                match = conflict_pattern.search(answer)
                if match:
                    split_point = match.start(1) + 1  # Include the period
                    answer = answer[:split_point] + '</p>'

            # Specific pattern for the exact example provided by the user
            exact_pattern = re.compile(r'(Renewed Violence Escalates in Gaza Conflict:(?:\s*<br>|\s*\n)?(?:\s*[-–—]\s*[^.!?<>]*(?:<br>|\s*\n)?)+)', re.IGNORECASE)
            if exact_pattern.search(answer):
                match = exact_pattern.search(answer)
                if match:
                    answer = answer.replace(match.group(1), '')

                    # If we removed content, make sure we still have a proper ending
                    if not re.search(r'[.!?][^<>]{10,}</p>$', answer):
                        # Instead of using predefined conclusions, just ensure the answer has a proper ending
                        if not answer.endswith('</p>'):
                            answer += '</p>'

            # Step 6: Remove any dash-prefixed items anywhere in the text
            lines = answer.split('\n')
            filtered_lines = []
            for line in lines:
                if not re.match(r'\s*[-–—]\s+', line):  # Skip lines that start with dashes
                    filtered_lines.append(line)
            answer = '\n'.join(filtered_lines)

            # Step 7: Detect any paragraph with a title followed by list-like items and remove it
            answer = re.sub(r'<p>[^.!?<>]*:(?:\s*<br>|\s*\n)?\s*(?:[-–—•*⦁◦‣⁃▪▹>][^.!?<>]*(?:<br>|\n)?)+\s*</p>', '', answer)

            # IMPROVED Step 6: Only remove incomplete dash-prefixed items, not all dashes
            # Remove only lines that start with dashes but don't end with proper punctuation
            lines = answer.split('\n')
            filtered_lines = []
            for line in lines:
                # Only skip dash lines that don't end with proper punctuation and aren't part of a list
                if re.match(r'\s*[-–—]\s+[^.!?]*$', line) and not re.search(r'</li>|<li>', line):
                    continue  # Skip incomplete dash items
                filtered_lines.append(line)
            answer = '\n'.join(filtered_lines)

            # IMPROVED Step 7: Only remove problematic title + list combinations
            # Only target titles followed by incomplete list items (no proper punctuation)
            answer = re.sub(r'<p>[^.!?<>]*:(?:\s*<br>|\s*\n)?\s*(?:[-–—•*⦁◦‣⁃▪▹>][^.!?<>]*?(?!</li>)(?![.!?])(?:<br>|\n)?)+\s*</p>', '', answer)

            # Step 8: Ensure the content ends with proper punctuation
            if not re.search(r'[.!?]</p>$', answer) and not answer.endswith('</ul><p>'):
                answer = re.sub(r'(</p>)$', '.</p>', answer)

            # Fix double periods that might appear in the text (like "dynamics. .")
            answer = re.sub(r'([.!?])\s*\.', r'\1', answer)
            answer = re.sub(r'\.{2,}', '.', answer)  # Replace multiple periods with a single one

            # Step 9: Ensure the final paragraph is a proper conclusion, not a list or title
            if not re.search(r'[.!?][^<>]{10,}</p>$', answer) or answer.count('</p>') < 2:
                # If the final paragraph doesn't have a proper ending sentence, add a generic conclusion
                # Remove any short headline-like ending paragraph
                answer = re.sub(r'<p>[^<>]{1,100}</p>\s*$', '', answer)

                # Only add conclusion if we actually need one (content is too short or ends abruptly)
                if answer.count('</p>') < 2 or not re.search(r'[.!?][^<>]{10,}</p>$', answer):
                    # Instead of using predefined conclusions, just ensure the answer has a proper ending
                    if not answer.endswith('</p>'):
                        answer += '</p>'

            # Format the answer in a card
            formatted_answer = f"""
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">
                            <i class="bi bi-question-circle-fill me-2 text-primary"></i>
                            {question}
                        </h6>
                        <div class="ai-answer mt-3">
                            <div class="answer-content">
                                {answer}
                            </div>
                        </div>
                        {citation_html}
                        <div class="text-muted mt-3 small">
                            <i class="bi bi-info-circle me-1"></i>Answer generated using AI and real-time web search results.
                        </div>
                    </div>
                </div>
            """

        except Exception as formatting_error:
            print(f"Error in answer formatting: {str(formatting_error)}")
            # If formatting fails, create a simple answer without advanced formatting
            formatted_answer = f"""
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">
                            <i class="bi bi-question-circle-fill me-2 text-primary"></i>
                            {question}
                        </h6>
                        <div class="ai-answer mt-3">
                            <div class="answer-content">
                                <p>{answer}</p>
                            </div>
                        </div>
                        <div class="text-muted mt-3 small">
                            <i class="bi bi-info-circle me-1"></i>Answer generated using AI and real-time web search results.
                        </div>
                    </div>
                </div>
            """

        # Cache the answer with consistent structure - wrap in try-catch to prevent caching errors
        try:
            background_cache[cache_key] = {
                'answer': formatted_answer,
                'timestamp': time.time(),
                'complete': True
            }
        except Exception as cache_error:
            print(f"Error caching answer: {str(cache_error)}")
            # Continue without caching

        return jsonify({'answer': formatted_answer})

    except Exception as e:
        current_app.logger.error(f"Error in ask_anything: {str(e)}")
        print(f"Unexpected error in ask_anything: {str(e)}")
        # Create an error response
        error_answer = f"""
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2 text-muted">
                        <i class="bi bi-question-circle-fill me-2 text-primary"></i>
                        {question if 'question' in locals() else 'Your question'}
                    </h6>
                    <div class="ai-answer mt-3">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Error processing your question. Please try again.
                        </div>
                    </div>
                </div>
            </div>
        """
        return jsonify({'answer': error_answer}), 200  # Return 200 to avoid frontend error handling

@main.route('/get_article_content', methods=['POST'])
def get_article_content():
    """Retrieve the full content of an article."""
    data = request.json
    url = data.get('url', '')
    title = data.get('title', '')
    api_content = data.get('api_content', '')
    extraction_method = "Unknown"
    author = None
    publication_date = None

    if not url:
        return jsonify({
            'error': 'No URL provided',
            'message': 'Please provide a valid URL to retrieve article content.'
        }), 400

    try:
        article_content = ""

        # PRIORITY 1: Use content directly from the API if available
        if api_content and len(api_content) > 150:
            article_content = api_content
            extraction_method = "NewsData API content"
            print(f"Using content from NewsData API: {len(article_content)} characters")

        # PRIORITY 2: Fall back to newspaper3k extraction if API content not available
        else:
            print("API content unavailable, extracting from URL")
            try:
                # Get more detailed article information using newspaper3k
                from newspaper import Article

                # Initialize and download the article
                article = Article(url)
                article.download()
                article.parse()

                # Get article text
                article_content = article.text

                # Get additional metadata
                if not title and article.title:
                    title = article.title

                author = article.authors[0] if article.authors else None
                publication_date = article.publish_date.strftime('%Y-%m-%d') if article.publish_date else None

                extraction_method = "newspaper3k"
                print(f"Article content extracted via newspaper3k: {len(article_content)} characters")

            except Exception as extract_error:
                print(f"Error extracting content with newspaper3k: {str(extract_error)}")
                # Fall back to basic extractor
                extraction_result = summarizer.extract_content(url)
                if isinstance(extraction_result, dict):
                    article_content = extraction_result.get("content", "")
                else:
                    article_content = extraction_result

                extraction_method = "Basic extractor"

        # Check if we got content
        if not article_content or len(article_content) < 100:
            return jsonify({
                'error': 'Content extraction failed',
                'message': 'Unable to extract content from the provided URL.'
            }), 404

        # Format the response
        return jsonify({
            'title': title,
            'content': article_content,
            'author': author,
            'publication_date': publication_date,
            'extraction_method': extraction_method
        })

    except Exception as e:
        current_app.logger.error(f"Error retrieving article content: {str(e)}")
        return jsonify({
            'error': 'Processing error',
            'message': f'An error occurred while retrieving the article content: {str(e)}'
        }), 500

@main.route('/proxy_article', methods=['GET'])
def proxy_article():
    """
    Proxy service that fetches an article and serves it without X-Frame-Options restrictions
    to allow embedding in an iframe.
    """
    url = request.args.get('url')

    if not url:
        return jsonify({"error": "No URL provided"}), 400

    if not url.startswith('http'):
        return jsonify({"error": "Invalid URL format"}), 400

    try:
        # Fetch the content with a browser-like User-Agent
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=15)

        if response.status_code != 200:
            return jsonify({"error": f"Failed to fetch content, status code: {response.status_code}"}), 502

        # Create a Flask response with the content
        proxy_response = make_response(response.text)

        # Set Content-Type header based on the response
        content_type = response.headers.get('Content-Type', 'text/html')
        proxy_response.headers['Content-Type'] = content_type

        # Remove headers that prevent framing
        proxy_response.headers['X-Frame-Options'] = 'ALLOWALL'
        proxy_response.headers['Content-Security-Policy'] = "frame-ancestors *"

        # Add a base tag to make relative URLs work correctly
        if 'text/html' in content_type:
            html = response.text
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            # Add base tag to handle relative URLs
            html = html.replace('<head>', f'<head><base href="{base_url}">')

            # Fix mixed content issues by ensuring all resources use HTTPS
            html = html.replace('http://', 'https://')

            proxy_response.set_data(html)

        return proxy_response

    except requests.RequestException as e:
        return jsonify({"error": f"Error fetching content: {str(e)}"}), 502
    except Exception as e:
        return jsonify({"error": f"Unexpected error: {str(e)}"}), 500

# Admin routes
@main.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if is_admin():
        return redirect(url_for('main.admin_dashboard'))

    error = None

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        admin = Admin.query.filter_by(username=username).first()

        if admin and admin.check_password(password):
            session['admin_id'] = admin.id
            session['is_admin'] = True

            # Also set user_id if not already set
            if 'user_id' not in session:
                # Create or get a user for the admin
                user = User.query.filter_by(email=f"{username}@admin.birdii.com").first()
                if not user:
                    user = User(email=f"{username}@admin.birdii.com")
                    db.session.add(user)
                    db.session.commit()

                session['user_id'] = user.id
                session['email'] = user.email

            return redirect(url_for('main.admin_dashboard'))
        else:
            error = 'Invalid username or password'

    return render_template('admin/login.html', error=error)

@main.route('/admin/logout')
def admin_logout():
    # Clear admin session variables
    session.pop('admin_id', None)
    session.pop('is_admin', None)

    # Redirect to admin login
    return redirect(url_for('main.admin_login'))

@main.route('/admin/dashboard')
def admin_dashboard():
    if not is_admin():
        return redirect(url_for('main.admin_login'))

    # Get statistics
    total_users = User.query.count()
    today = datetime.now().date()
    active_today = User.query.filter(User.last_visit >= today).count()
    visits_today = db.session.query(db.func.sum(User.visit_count)).filter(User.last_visit >= today).scalar() or 0

    # Get recent users
    recent_users = User.query.order_by(User.last_visit.desc()).limit(10).all()

    stats = {
        'total_users': total_users,
        'active_today': active_today,
        'visits_today': visits_today
    }

    return render_template('admin/dashboard.html', stats=stats, recent_users=recent_users)

@main.route('/admin/users')
def admin_users():
    if not is_admin():
        return redirect(url_for('main.admin_login'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # Get paginated users
    users_pagination = User.query.order_by(User.created_at.desc()).paginate(page=page, per_page=per_page)

    # Prepare pagination data
    pagination = {
        'page': page,
        'per_page': per_page,
        'total': users_pagination.total,
        'pages': users_pagination.pages
    }

    return render_template('admin/users.html', users=users_pagination.items, pagination=pagination)