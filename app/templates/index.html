{% extends "base.html" %}

{% block content %}
<style>
    /* Common variables for consistent styling */
    :root {
        --font-franklin: 'Franklin Gothic Medium', '<PERSON>l Narrow', Arial, sans-serif;
        --font-cheltenham: Georgia, 'Times New Roman', Times, serif;
        --color-primary: #333333;
        --color-secondary: #3B82F6;
        --color-border: #E5E7EB;
        --color-light-gray: #F3F4F6;
        --color-dark-gray: #4B5563;
        --color-black: #1F2937;
        --border-radius-sm: 0.25rem;
    }

    /* Article Styling */
    body {
        font-family: var(--font-franklin);
        color: var(--color-dark-gray);
        background-color: #F9FAFB;
    }

    .article-content {
        background-color: white;
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        max-height: 600px;
        overflow-y: auto;
    }

    .article-content h4 {
        font-family: var(--font-cheltenham);
        color: var(--color-black);
        font-weight: 700;
        letter-spacing: -0.01em;
    }

    .article-body {
        font-family: var(--font-cheltenham);
        font-size: 1.05rem;
        line-height: 1.6;
        color: var(--color-black);
    }

    .article-body p {
        margin-bottom: 1.2rem;
    }

    .article-meta {
        font-size: 0.9rem;
        color: var(--color-dark-gray);
    }

    /* Card & Container Styling */
    .card {
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.2s ease;
        height: 100%;
        overflow: hidden;
    }

    /* Styles for article images */
    .article-img-wrapper {
        height: 250px;
        overflow: hidden;
        position: relative;
        border-bottom: 1px solid var(--color-border);
    }

    .article-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .card:hover .article-img {
        transform: scale(1.03);
    }

    /* Subtle gradient overlay for better text visibility */
    .article-img-wrapper::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.3));
        z-index: 1;
    }

    /* NYT-inspired card styles */
    #articles-container .card {
        box-shadow: none;
        border-radius: var(--border-radius-sm);
        border: 1px solid var(--color-border);
        transition: all 0.2s ease;
    }

    #articles-container .card:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
    }

    #articles-container .card .article-image-container {
        border-top-left-radius: var(--border-radius-sm);
        border-top-right-radius: var(--border-radius-sm);
        overflow: hidden;
    }

    /* NYT-inspired context styles */
    .enriched-context {
        padding: var(--spacing-lg);
        background-color: white;
        border-radius: var(--border-radius-sm);
        margin-top: var(--spacing-md);
        border: 1px solid var(--color-border);
        position: relative;
    }

    .enriched-context .close-btn {
        position: relative;
        display: block;
        margin: 15px auto 0;
        background-color: var(--color-light-gray);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        font-size: 0.9rem;
        color: var(--color-dark-gray);
        cursor: pointer;
        padding: 6px 12px;
        line-height: 1.2;
        text-align: center;
        width: 100%;
    }

    .enriched-context .close-btn:hover {
        background-color: var(--color-border);
        color: var(--color-black);
    }

    .enriched-context .context-content {
        font-family: var(--font-imperial);
        line-height: 1.6;
        text-align: left;
        font-size: 1.05rem;
    }

    .enriched-context strong {
        display: block;
        font-family: var(--font-franklin);
        margin-top: var(--spacing-lg);
        margin-bottom: var(--spacing-sm);
        font-size: 1.15rem;
        color: var(--color-black);
        font-weight: 700;
        letter-spacing: -0.01em;
    }

    .enriched-context p {
        margin-bottom: var(--spacing-md);
    }

    .enriched-context .citations {
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--color-border);
        font-family: var(--font-franklin);
    }

    .enriched-context .citations ol {
        padding-left: 25px;
        margin-bottom: 0;
    }

    .enriched-context .citations li {
        margin-bottom: var(--spacing-xs);
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .enriched-context .citations a,
    .sources a {
        color: var(--color-link);
        text-decoration: none;
    }

    .enriched-context .citations a:hover,
    .sources a:hover {
        color: var(--color-link-hover);
        text-decoration: underline;
    }

    /* Ask Anything styles */
    .ai-answer .answer-content {
        font-family: var(--font-imperial);
        line-height: 1.6;
        color: var(--color-dark-gray);
        font-size: 1.05rem;
    }

    .ai-answer strong {
        display: inline;
        font-weight: 600;
        color: var(--color-black);
    }

    .ai-answer p {
        margin-bottom: var(--spacing-md);
    }

    .ai-answer ul {
        margin-bottom: var(--spacing-md);
        padding-left: 20px;
    }

    .ai-answer li {
        margin-bottom: var(--spacing-xs);
    }

    .ai-answer ul {
        margin-bottom: var(--spacing-md);
        padding-left: 20px;
        list-style-type: disc;
    }

    .ai-answer li {
        margin-bottom: var(--spacing-xs);
        line-height: 1.5;
        font-family: var(--font-imperial);
        color: var(--color-dark-gray);
    }

    .ai-answer li::marker {
        color: var(--color-black);
    }

    .sources {
        border-top: 1px solid var(--color-border);
        padding-top: var(--spacing-md);
        margin-top: var(--spacing-md);
        background-color: var(--color-off-white);
        padding: var(--spacing-md);
        border-radius: var(--border-radius-sm);
        font-family: var(--font-franklin);
    }

    .sources ol {
        padding-left: 20px;
        margin-bottom: 0;
    }

    .sources li {
        margin-bottom: var(--spacing-xs);
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .sources .text-muted,
    .citations .text-muted {
        color: var(--color-medium-gray) !important;
        font-size: 0.8rem;
    }

    /* Update styles for summary content and ask anything containers */
    .summary-content .summary-text,
    .summary-content .key-points-list li,
    .ask-anything-results p,
    .ask-anything-results div,
    .enriched-context p,
    .enriched-context li {
        text-align: left;
    }

    /* NYT-style buttons */
    .welcome-topic-btn {
        font-family: var(--font-franklin);
        text-transform: none;
        letter-spacing: -0.01em;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: var(--border-radius-sm);
        padding: 0.4rem 0.8rem;
        border: 1px solid var(--color-border);
        color: var(--color-black);
        background-color: white;
    }

    .welcome-topic-btn:hover {
        background-color: var(--color-light-gray);
        color: var(--color-black);
        border-color: var(--color-black);
    }

    .welcome-topic-btn.active {
        background-color: #f2f2f2 !important;
        color: var(--color-black) !important;
        border-color: var(--color-dark-gray) !important;
        font-weight: 600;
    }

    .topic-btn.active,
    .welcome-topic-btn.active {
        background-color: #f2f2f2 !important;
        color: var(--color-black) !important;
        border-color: var(--color-dark-gray) !important;
        font-weight: 600;
    }

    .topic-btn:hover,
    .welcome-topic-btn:hover {
        border-color: var(--color-primary);
        color: var(--color-primary);
    }

    /* Newsletter button styling - now consistent with news topics */
    .newsletter-btn {
        /* Inherit default topic-btn styling */
    }

    .newsletter-btn:hover {
        /* Inherit default topic-btn hover styling */
    }

    .newsletter-btn.active {
        /* Inherit default topic-btn active styling */
    }

    .newsletter-sidebar-btn {
        /* Inherit default styling */
    }

    .newsletter-sidebar-btn:hover {
        /* Inherit default styling */
    }

    .newsletter-sidebar-btn.active {
        /* Inherit default styling */
    }

    /* Make enriched context titles Sans Serif */
    .enriched-context h5,
    .enriched-context h6,
    .enriched-context strong {
        font-family: var(--font-franklin);
        font-weight: 700;
        color: var(--color-black);
    }

    .enriched-context strong {
        display: block;
        margin-top: var(--spacing-lg);
        margin-bottom: var(--spacing-sm);
        font-size: 1.15rem;
        letter-spacing: -0.01em;
    }

    /* NYT-style headings within cards */
    #articles-container .card-title {
        font-family: var(--font-cheltenham);
        font-weight: 700;
        font-size: 1.3rem;
        line-height: 1.3;
        margin-bottom: var(--spacing-sm);
        margin-top: 1.2rem;
        letter-spacing: -0.01em;
        color: var(--color-black);
    }

    #articles-container .card-text {
        font-family: var(--font-imperial);
        color: var(--color-dark-gray);
        line-height: 1.5;
        font-size: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: 4.5em; /* 3 lines x 1.5 line-height */
        text-overflow: ellipsis;
    }

    /* Source badge styling */
    .source-badge.bg-primary {
        background-color: #00008B !important; /* Blue color for all source badges */
        font-family: var(--font-franklin);
        text-transform: uppercase;
        font-size: 0.75rem;
        font-weight: 500;
        letter-spacing: 0.02em;
        padding: 0.35em 0.65em;
        border-radius: var(--border-radius-sm);
        color: white;
        border: none;
    }

    /* Date styling */
    .text-muted.date {
        font-family: var(--font-franklin);
        font-size: 0.8rem;
        letter-spacing: 0.01em;
        color: var(--color-medium-gray) !important;
    }

    /* Summary section styling */
    .summary-section h6 {
        font-family: var(--font-franklin);
        text-transform: uppercase;
        letter-spacing: 0.02em;
        font-size: 0.85rem;
        font-weight: 700;
        color: var(--color-black);
        margin-bottom: var(--spacing-sm);
    }

    .summary-text {
        font-family: var(--font-imperial);
        font-size: 1.05rem;
        line-height: 1.6;
        color: var(--color-dark-gray);
    }

    .key-points-list {
        font-family: var(--font-imperial);
    }

    .key-points-list li {
        margin-bottom: var(--spacing-sm);
        line-height: 1.5;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        flex-wrap: nowrap; /* Prevent wrapping to ensure all buttons stay on same line */
        margin-bottom: 10px;
        /* Removed overflow-x and scrollbar styles */
    }

    /* Removed scrollbar styling for webkit and firefox */

    .action-buttons .btn {
        flex: 0 0 auto; /* Don't let buttons grow or shrink */
        white-space: nowrap; /* Prevent text wrapping within buttons */
    }

    /* Medium screens - use abbreviated text and smaller buttons */
    @media (max-width: 992px) and (min-width: 577px) {
        .action-buttons .btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.75rem !important;
            margin-right: 0.35rem !important;
        }

        .action-buttons .btn i {
            font-size: 0.75rem;
            margin-right: 0.15rem;
        }

        /* Use abbreviated text for buttons on medium screens */
        .action-buttons .open-article-here-btn::after {
            content: "Open";
        }

        .action-buttons .open-article-here-btn span {
            display: none;
        }

        .action-buttons .summarize-btn::after {
            content: "Summary";
        }

        .action-buttons .summarize-btn span {
            display: none;
        }

        .action-buttons .context-btn::after {
            content: "Context";
        }

        .action-buttons .context-btn span {
            display: none;
        }

        .action-buttons .ask-anything-btn::after {
            content: "Ask";
        }

        .action-buttons .ask-anything-btn span {
            display: none;
        }
    }

    /* Small screens - organize buttons in two rows with specific arrangement */
    @media (max-width: 576px) {
        .action-buttons {
            display: flex;
            flex-wrap: wrap !important; /* Allow wrapping on small screens */
            justify-content: flex-start;
        }

        .action-buttons .btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.75rem !important;
            margin-right: 0.4rem !important;
            margin-bottom: 0.4rem !important;
        }

        .action-buttons .btn i {
            font-size: 0.75rem;
        }

        /* First row buttons - full width to ensure they're on their own row */
        .action-buttons .first-row-btn {
            flex: 1 0 auto; /* Allow some growth but no shrink */
            margin-bottom: 0.4rem;
        }

        /* Second row buttons */
        .action-buttons .context-btn,
        .action-buttons .ask-anything-btn,
        .action-buttons .notes-btn {
            flex: 1 0 auto; /* Distribute available space equally */
            text-align: center;
        }

        /* Use abbreviated text for second row */
        .action-buttons .context-btn span::after {
            content: "Context";
        }

        .action-buttons .context-btn span {
            font-size: 0.7rem;
        }

        .action-buttons .ask-anything-btn span::after {
            content: "Ask";
        }

        .action-buttons .ask-anything-btn span {
            font-size: 0.7rem;
        }

        /* Reset any content styles from medium screens */
        .action-buttons .btn::after {
            content: none !important;
        }

        /* Hide the original text but show our replacement */
        .action-buttons .context-btn span span,
        .action-buttons .ask-anything-btn span span {
            display: none !important;
        }
    }

    /* Active state styling with gradient matching the source badges */
    .action-buttons .btn.active {
        background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%) !important;
        color: #FFFFFF !important; /* WHITE text with !important to ensure it applies */
        border: none !important;
        font-family: var(--font-franklin);
        font-weight: 500;
        letter-spacing: 0.02em;
        text-transform: none !important; /* Changed from lowercase to none to allow mixed case */
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
        border-radius: var(--border-radius-sm);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important; /* Add shadow for consistency */
        text-shadow: 0 1px 1px rgba(0,0,0,0.2);
    }

    /* Make sure all default button styles are overridden when active */
    .action-buttons .read-more-btn.active,
    .action-buttons .summarize-btn.active,
    .action-buttons .context-btn.active,
    .action-buttons .ask-anything-btn.active,
    .action-buttons .notes-btn.active {
        background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%) !important;
        color: #FFFFFF !important;
        text-transform: none !important; /* Changed from lowercase to none to allow mixed case */
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* Ask button styling */
    .ask-anything-form .btn-primary {
        background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%);
        border: none;
        color: white;
        text-shadow: 0 1px 1px rgba(0,0,0,0.2);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .ask-anything-form .btn-primary:hover {
        background: linear-gradient(90deg, #0986e0 0%, #42bd7f 100%);
        color: white;
        box-shadow: 0 3px 5px rgba(0,0,0,0.15);
    }

    /* Read Article button styling */
    .btn-read-article,
    .btn-action {
        background-color: var(--color-primary); /* Light dark for Read Article button (was teal) */
        border-color: var(--color-primary);
        color: white;
    }

    .btn-read-article:hover,
    .btn-action:hover {
        background-color: #444444; /* Darker light dark (was darker teal) */
        border-color: #444444;
        color: white;
    }

    /* Make article content close button match enriched context close button */
    .article-content .close-btn {
        position: relative;
        display: block;
        margin: 15px auto 0;
        background-color: var(--color-light-gray);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        font-size: 0.9rem;
        color: var(--color-dark-gray);
        cursor: pointer;
        padding: 6px 12px;
        line-height: 1.2;
        text-align: center;
        width: 100%;
    }

    .article-content .close-btn:hover {
        background-color: var(--color-border);
        color: var(--color-black);
    }

    .summary-section .close-btn {
        position: relative;
        display: block;
        margin: 15px auto 0;
        background-color: var(--color-light-gray);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        font-size: 0.9rem;
        color: var(--color-dark-gray);
        cursor: pointer;
        padding: 6px 12px;
        line-height: 1.2;
        text-align: center;
        width: 100%;
    }

    .summary-section .close-btn:hover {
        background-color: var(--color-border);
        color: var(--color-black);
    }

    /* Ask Anything close button styling */
    .ask-anything-results .close-btn {
        position: relative;
        display: block;
        margin: 15px auto 0;
        background-color: var(--color-light-gray);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        font-size: 0.9rem;
        color: var(--color-dark-gray);
        cursor: pointer;
        padding: 6px 12px;
        line-height: 1.2;
        text-align: center;
        width: 100%;
    }

    .ask-anything-results .close-btn:hover {
        background-color: var(--color-border);
        color: var(--color-black);
    }

    /* Iframe container styling */
    .iframe-container {
        height: 600px;
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: var(--border-radius-sm);
        border: 1px solid var(--color-border);
        background-color: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        margin-bottom: var(--spacing-md);
    }

    .iframe-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
        opacity: 1; /* Changed from 0 to 1 to make iframe visible by default */
        transition: opacity 0.5s ease;
        z-index: 5; /* Ensure iframe is below the loading overlay */
    }

    .iframe-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: rgba(255,255,255,0.98);
        z-index: 10;
        transition: opacity 0.3s ease;
        padding: 1rem;
        text-align: center;
    }

    .iframe-loading-overlay .loading-message {
        margin-top: 1rem;
        max-width: 80%;
    }

    .iframe-loading-overlay .spinner-border {
        width: 3rem;
        height: 3rem;
        margin-bottom: 0.5rem;
    }

    /* Responsive height adjustments for iframe */
    @media (max-width: 768px) {
        .iframe-container {
            height: 450px;
        }
    }

    @media (max-width: 576px) {
        .iframe-container {
            height: 350px;
        }
    }

    /* Aligning source badge and date */
    .card-body .mb-2.d-flex {
        align-items: center; /* This will vertically center the items */
    }

    /* Sticky topics sidebar */
    .sticky-sidebar {
        position: sticky;
        top: 20px; /* Distance from the top of the viewport when sticky */
        max-height: calc(100vh - 40px); /* Viewport height minus top and bottom margin */
        overflow-y: auto; /* Add scrollbar if content is too tall */
        z-index: 100; /* Ensure it stays above other content when scrolling */
        transition: all 0.2s ease; /* Smooth transition when becoming sticky */
    }

    /* Ensure the main content has enough space for proper layout with sticky sidebar */
    #content-section .row {
        display: flex;
        flex-wrap: wrap;
    }

    @media (max-width: 767px) {
        /* On mobile, don't make the sidebar sticky */
        .sticky-sidebar {
            position: relative;
            top: 0;
            max-height: none;
            margin-bottom: 1rem;
        }

        /* Make sidebar full width on mobile */
        #content-section .col-md-3 {
            flex: 0 0 100%;
            max-width: 100%;
        }

        #content-section .col-md-9 {
            flex: 0 0 100%;
            max-width: 100%;
        }

        /* Adjust sidebar styling for mobile */
        .sticky-sidebar .card-header {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }

        .sticky-sidebar .list-group-item {
            font-size: 0.9rem !important;
            padding: 0.5rem 1rem !important;
        }

        .sticky-sidebar h6 {
            font-size: 0.8rem !important;
        }
    }

    @media (max-width: 576px) {
        /* Further mobile optimizations */
        .sticky-sidebar .card-header {
            font-size: 0.85rem;
            padding: 0.4rem 0.6rem;
        }

        .sticky-sidebar .list-group-item {
            font-size: 0.85rem !important;
            padding: 0.4rem 0.8rem !important;
        }

        .sticky-sidebar h6 {
            font-size: 0.75rem !important;
            padding: 0 0.6rem !important;
        }

        /* Adjust article cards for mobile */
        #articles-container .card {
            margin-bottom: 1rem;
        }

        #articles-container .card-title {
            font-size: 1.1rem !important;
            line-height: 1.2;
        }

        #articles-container .card-text {
            font-size: 0.9rem !important;
        }

        /* Make source badges and dates smaller */
        .source-badge {
            font-size: 0.65rem !important;
            padding: 0.2rem 0.4rem !important;
        }

        .text-muted.date {
            font-size: 0.7rem !important;
        }
    }

    /* Popup dropdown styles for topic buttons */
    .topic-dropdown {
        position: relative;
        display: inline-block;
    }

    .topic-dropdown-content {
        display: none;
        position: absolute;
        background-color: white;
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-sm);
        z-index: 1000;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 5px;
        padding: 0.5rem 0;
    }

    .topic-dropdown-content.show {
        display: block;
    }

    .topic-dropdown-item {
        display: block;
        padding: 0.5rem 1rem;
        text-decoration: none;
        color: var(--color-black);
        font-family: var(--font-franklin);
        font-size: 0.9rem; /* Consistent font size for all screens */
        border: none;
        background: none;
        width: 100%;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .topic-dropdown-item:hover {
        background-color: var(--color-light-gray);
        color: var(--color-black);
    }

    .main-topic-btn {
        font-family: var(--font-franklin);
        text-transform: none;
        letter-spacing: -0.01em;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: var(--border-radius-sm);
        padding: 0.8rem 1.5rem;
        border: 1px solid var(--color-border);
        color: var(--color-black);
        background-color: white;
        margin: 0 1.5rem 1rem 1.5rem;
        transition: all 0.2s ease;
        cursor: pointer;
        min-width: 180px;
    }

    .main-topic-btn:hover {
        background-color: var(--color-light-gray);
        color: var(--color-black);
        border-color: var(--color-black);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .main-topic-btn.active {
        background-color: var(--color-light-gray);
        border-color: var(--color-black);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* Mobile responsiveness for main topic buttons */
    @media (max-width: 768px) {
        .main-topic-btn {
            font-size: 1rem;
            padding: 0.7rem 1.2rem;
            margin: 0 0.5rem 1rem 0.5rem;
            min-width: 160px;
        }

        .welcome-message p.lead {
            font-size: 1rem !important;
            padding: 0 1rem;
        }

        .topic-dropdown-content {
            min-width: 180px;
            left: 0;
            transform: none;
            margin-left: 0.5rem;
        }

        /* Remove font size override to keep consistent 0.9rem */
    }

    @media (max-width: 576px) {
        .main-topic-btn {
            font-size: 0.95rem;
            padding: 0.6rem 1rem;
            margin: 0 0.25rem 0.75rem 0.25rem;
            min-width: 140px;
            width: calc(50% - 0.5rem);
        }

        .welcome-message p.lead {
            font-size: 0.95rem !important;
            padding: 0 0.5rem;
        }

        .topic-dropdown {
            width: calc(50% - 0.5rem);
            margin: 0 0.25rem;
        }

        .topic-dropdown-content {
            min-width: 160px;
            left: 0;
            transform: none;
            margin-left: 0;
            width: 100%;
        }

        /* Remove font size override to keep consistent 0.9rem */

        /* Adjust main buttons container */
        .mb-5.d-flex {
            margin-bottom: 3rem !important;
            margin-top: 2rem !important;
        }

        /* Make source badges smaller on mobile */
        .sources-container .badge {
            font-size: 0.7rem !important;
            padding: 0.25rem 0.5rem !important;
        }
    }

    @media (max-width: 480px) {
        .main-topic-btn {
            font-size: 0.9rem;
            padding: 0.5rem 0.8rem;
            min-width: 120px;
        }

        .topic-dropdown-content {
            min-width: 140px;
        }

        /* Remove font size override to keep consistent 0.9rem */
    }

    /* Header and navigation responsiveness */
    @media (max-width: 768px) {
        /* Adjust header spacing */
        .container-fluid.py-4 {
            padding-top: 1.5rem !important;
            padding-bottom: 1.5rem !important;
        }

        /* Make logo and title more compact */
        .d-flex.align-items-center h1 {
            font-size: 1.5rem !important;
            margin-bottom: 0 !important;
        }

        .d-flex.align-items-center img {
            width: 30px !important;
            height: 30px !important;
            margin-right: 0.5rem !important;
        }

        /* Adjust welcome message */
        .welcome-message {
            font-size: 0.9rem !important;
            margin-bottom: 1rem !important;
        }

        /* Make topic buttons container more compact */
        .d-flex.flex-wrap.gap-2.mb-4 {
            gap: 0.5rem !important;
            margin-bottom: 1.5rem !important;
        }
    }

    @media (max-width: 576px) {
        /* Further header optimizations */
        .container-fluid.py-4 {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }

        .d-flex.align-items-center h1 {
            font-size: 1.3rem !important;
        }

        .d-flex.align-items-center img {
            width: 25px !important;
            height: 25px !important;
        }

        .welcome-message {
            font-size: 0.85rem !important;
            margin-bottom: 0.75rem !important;
        }

        .d-flex.flex-wrap.gap-2.mb-4 {
            gap: 0.3rem !important;
            margin-bottom: 1rem !important;
        }
    }

    @media (max-width: 480px) {
        /* Extra small screens */
        .container-fluid {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        .d-flex.align-items-center h1 {
            font-size: 1.2rem !important;
        }

        .welcome-message {
            font-size: 0.8rem !important;
            line-height: 1.3;
        }
    }

    /* Topics Modal Styling */
    .topics-modal-btn {
        font-family: var(--font-franklin);
        font-weight: 650; /* Increased from 550 to 650 for bolder text */
        font-size: 0.8rem; /* Further reduced from 0.85rem to be smaller */
        padding: 0.4rem 0.8rem; /* Further reduced padding for minimal presence */
        border-radius: var(--border-radius-sm);
        transition: all 0.3s ease; /* Slower transition for subtlety */
        width: 100%; /* Make button span full width */
        background-color: rgba(0, 0, 0, 0.01); /* Almost transparent */
        border: 1px solid rgba(0, 0, 0, 0.03); /* Barely visible border */
        color: rgba(75, 85, 99, 0.6); /* More transparent text color */
        opacity: 0.7; /* Overall reduced opacity */
    }

    .topics-modal-btn:hover {
        transform: none; /* Remove transform to avoid distraction */
        box-shadow: none; /* Remove shadow completely */
        background-color: rgba(0, 0, 0, 0.04); /* Slightly more visible on hover */
        border-color: rgba(0, 0, 0, 0.08);
        opacity: 0.9; /* Slightly more visible on hover */
    }

    /* Active state for Browse Topics button with birdii gradient */
    .topics-modal-btn.active,
    .topics-modal-btn:focus,
    .topics-modal-btn[aria-expanded="true"] {
        background: linear-gradient(90deg, rgba(10, 149, 251, 0.3) 0%, rgba(75, 210, 143, 0.3) 100%) !important; /* Much more transparent gradient */
        border-color: rgba(10, 149, 251, 0.2) !important; /* Subtle border */
        color: rgba(255, 255, 255, 0.9) !important; /* Slightly transparent white text */
        box-shadow: none !important; /* No shadow */
        opacity: 0.6; /* Very subtle when active */
    }

    /* Sticky positioning for Browse Topics button container */
    #content-section .col-12.mb-3 {
        position: sticky;
        top: 0; /* Changed from 5px to 0 to remove gap at top */
        z-index: 50; /* Reduced z-index to be less intrusive */
        background-color: rgba(249, 250, 251, 0.7); /* More transparent background */
        backdrop-filter: blur(4px); /* Reduced blur for minimal effect */
        padding: 0.25rem 0; /* Minimal padding */
        margin-bottom: 0.5rem !important; /* Reduced margin */
        border-radius: var(--border-radius-sm);
    }

    .modal-topic-btn {
        transition: all 0.2s ease;
        border-radius: var(--border-radius-sm);
    }

    .modal-topic-btn:hover {
        /* Removed hover effects - no transform or box-shadow */
    }

    /* Override Bootstrap's default btn-outline-secondary hover effects for modal buttons */
    .modal-topic-btn.btn-outline-secondary:hover {
        background-color: transparent !important; /* No background change */
        border-color: #6c757d !important; /* Keep original border color */
        color: #6c757d !important; /* Keep original text color */
    }

    .modal-topic-btn.active {
        background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%) !important;
        border-color: transparent !important;
        color: white !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    }

    /* Remove the sticky sidebar styles since we're not using it anymore */
    .sticky-sidebar {
        display: none;
    }
</style>

<!-- Add Socket.IO client library -->
<script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

<div class="row mb-4 mt-3" id="welcome-section">
    <div class="col-12 text-center">
        <div class="welcome-message">
            <p class="lead mb-5" style="font-size: 1.1rem; margin-top: 1rem;">
                Choose a source & topic.<br>
                Experience news reading supercharged by AI.
            </p>

            <!-- Main Topic Buttons with Dropdowns -->
            <div class="mb-5 d-flex justify-content-center align-items-center flex-wrap" style="margin-bottom: 5rem !important; margin-top: 3rem !important;">
                <!-- News Articles Button with Dropdown -->
                <div class="topic-dropdown">
                    <button class="main-topic-btn">Major News Outlets</button>
                    <div class="topic-dropdown-content">
                        {% for topic in news_topics %}
                        <button class="topic-dropdown-item welcome-topic-btn" data-topic="{{ topic }}"
                                style="font-family: var(--font-franklin); letter-spacing: -0.01em; padding: 0.5rem 1rem; border-radius: 0; color: var(--color-dark-gray); transition: all 0.2s ease;">
                            {{ topic|safe }}
                        </button>
                        {% endfor %}
                    </div>
                </div>

                <!-- Newsletters Button with Dropdown -->
                <div class="topic-dropdown">
                    <button class="main-topic-btn">Blogs & More</button>
                    <div class="topic-dropdown-content">
                        {% for topic in newsletter_topics %}
                        <button class="topic-dropdown-item welcome-topic-btn newsletter-btn border-0" data-topic="{{ topic }}"
                                style="font-family: var(--font-franklin); letter-spacing: -0.01em; padding: 0.5rem 1rem; border-radius: 0; color: var(--color-dark-gray); transition: all 0.2s ease;">
                            {{ topic|safe }}
                        </button>
                        {% endfor %}
                    </div>
                </div>

                <!-- YouTube Channels Button with Dropdown -->
                <div class="topic-dropdown">
                    <button class="main-topic-btn">YouTube Channels</button>
                    <div class="topic-dropdown-content">
                        <button class="topic-dropdown-item welcome-youtube-btn" data-channel="bytebytego"
                                style="font-family: var(--font-franklin); letter-spacing: -0.01em; padding: 0.5rem 1rem; border-radius: 0; color: var(--color-dark-gray); transition: all 0.2s ease;">
                            ByteByteGo
                        </button>
                    </div>
                </div>
            </div>

            <!-- Premium sources list -->
            <div class="sources-container mt-5 pt-4">
                <div class="d-flex flex-wrap justify-content-center">
                    {% for source in premium_sources %}
                    <span class="badge m-1 source-badge" style="background-color: transparent; color: var(--color-dark-gray); border: 1px solid var(--color-border); font-weight: 500;">{{ source|upper }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div id="content-section" style="display: none;">
    <div class="row">
        <!-- Compact Topics Button (replaces the sidebar) -->
        <div class="col-12 mb-3">
            <button class="btn btn-outline-primary topics-modal-btn" data-bs-toggle="modal" data-bs-target="#topicsModal">
                <i class="bi bi-list"></i> Browse Topics
            </button>
        </div>

        <div class="col-12">
            <div id="articles-container">
                <!-- Empty container - removed the instruction text -->
            </div>
        </div>
    </div>
</div>

<!-- Topics Modal -->
<div class="modal fade" id="topicsModal" tabindex="-1" aria-labelledby="topicsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="topicsModalLabel" style="font-family: var(--font-franklin); font-weight: 700;">Browse Topics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Popular Sources Section -->
                <div class="mb-4">
                    <h6 class="mb-3 text-muted" style="font-family: var(--font-franklin); font-size: 0.85rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Major News Outlets</h6>
                    <div class="row">
                        {% for topic in news_topics %}
                        <div class="col-6 col-md-4 mb-2">
                            <button class="btn btn-outline-secondary w-100 topic-btn modal-topic-btn" data-topic="{{ topic }}" data-bs-dismiss="modal"
                                    style="font-family: var(--font-franklin); font-size: 0.9rem; padding: 0.5rem; text-align: center; transition: all 0.3s ease;">
                                {{ topic|safe }}
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Your Feed Section -->
                <div class="mb-4">
                    <h6 class="mb-3 text-muted" style="font-family: var(--font-franklin); font-size: 0.85rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Blogs & More</h6>
                    <div class="row">
                        {% for topic in newsletter_topics %}
                        <div class="col-6 col-md-4 mb-2">
                            <button class="btn btn-outline-secondary w-100 topic-btn newsletter-btn modal-topic-btn" data-topic="{{ topic }}" data-bs-dismiss="modal"
                                    style="font-family: var(--font-franklin); font-size: 0.9rem; padding: 0.5rem; text-align: center; transition: all 0.3s ease;">
                                {{ topic|safe }}
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- YouTube Channels Section -->
                <div class="mb-4">
                    <h6 class="mb-3 text-muted" style="font-family: var(--font-franklin); font-size: 0.85rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">YouTube Channels</h6>
                    <div class="row">
                        <div class="col-6 col-md-4 mb-2">
                            <button class="btn btn-outline-secondary w-100 topic-btn modal-topic-btn" data-topic="YouTube Channels" data-bs-dismiss="modal"
                                    style="font-family: var(--font-franklin); font-size: 0.9rem; padding: 0.5rem; text-align: center; transition: all 0.3s ease;">
                                ByteByteGo
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Article template -->
<template id="article-template">
    <div class="col-md-12 mb-4">
        <div class="card h-100">
            <!-- Image container (shown if available) -->
            <div class="article-image-container"></div>

            <div class="card-body">
                <div class="mb-2 d-flex align-items-center">
                    <span class="badge source-badge"></span>
                    <small class="text-muted ms-2 date d-flex align-items-center"></small>
                    <a href="#" class="read-more-btn ms-auto" style="font-family: var(--font-franklin); font-size: 0.8rem; color: var(--color-secondary); text-decoration: none;">
                        <i class="bi bi-box-arrow-up-right"></i> Read Article
                    </a>
                </div>
                <h5 class="card-title"></h5>
                <p class="card-text short-description">Click "Summarize" to generate an AI summary of this article.</p>

                <!-- Summary section (hidden by default) -->
                <div class="summary-section mt-3" style="display: none;">
                    <div class="summary-content">
                        <div class="summary mb-3">
                            <h6>Summary</h6>
                            <p class="summary-text"></p>
                        </div>
                        <div class="key-points mb-3">
                            <h6>Key Points</h6>
                            <ul class="key-points-list"></ul>
                        </div>
                    </div>
                </div>

                <!-- Action buttons - Moved below the summary section -->
                <div class="d-flex mt-2 mb-2 action-buttons">
                    <button class="btn btn-sm me-2 mb-2 open-article-here-btn first-row-btn" type="button"
                            style="font-family: var(--font-franklin); border-radius: var(--border-radius-sm);
                                   font-size: 0.8rem; letter-spacing: 0.01em; font-weight: 600; text-transform: none;
                                   padding: 0.35rem 0.7rem; color: var(--color-dark-gray); border: 1px solid var(--color-border);">
                        <i class="bi bi-book"></i> <span>Open Article Here</span>
                    </button>
                    <button class="btn btn-sm me-2 mb-2 summarize-btn first-row-btn"
                            style="font-family: var(--font-franklin); border-radius: var(--border-radius-sm);
                                   font-size: 0.8rem; letter-spacing: 0.01em; font-weight: 600; text-transform: none;
                                   padding: 0.35rem 0.7rem; color: var(--color-dark-gray); border: 1px solid var(--color-border);">
                        <i class="bi bi-list-ul"></i> <span>Summarize</span>
                    </button>
                    <button class="btn btn-sm me-2 mb-2 context-btn" data-title="" data-topic=""
                            style="font-family: var(--font-franklin); border-radius: var(--border-radius-sm);
                                   font-size: 0.8rem; letter-spacing: 0.01em; font-weight: 600; text-transform: none;
                                   padding: 0.35rem 0.7rem; color: var(--color-dark-gray); border: 1px solid var(--color-border);">
                        <i class="bi bi-lightbulb"></i> <span><span>Additional Context</span></span>
                    </button>
                    <button class="btn btn-sm me-2 mb-2 ask-anything-btn" data-title="" data-topic=""
                            style="font-family: var(--font-franklin); border-radius: var(--border-radius-sm);
                                   font-size: 0.8rem; letter-spacing: 0.01em; font-weight: 600; text-transform: none;
                                   padding: 0.35rem 0.7rem; color: var(--color-dark-gray); border: 1px solid var(--color-border);">
                        <i class="bi bi-question-circle"></i> <span><span>Ask Anything</span></span>
                    </button>
                    <button class="btn btn-sm me-2 mb-2 notes-btn" data-title="" data-topic=""
                            style="font-family: var(--font-franklin); border-radius: var(--border-radius-sm);
                                   font-size: 0.8rem; letter-spacing: 0.01em; font-weight: 600; text-transform: none;
                                   padding: 0.35rem 0.7rem; color: var(--color-dark-gray); border: 1px solid var(--color-border);">
                        <i class="bi bi-journal-text"></i> <span>Notes</span>
                    </button>
                </div>

                <!-- Ask Anything container (will be populated when Ask Anything button is clicked) -->
                <div class="ask-anything-container mt-3" style="display: none;"></div>

                <!-- Context container (will be populated when Additional Context button is clicked) -->
                <div class="context-container mt-3" style="display: none;"></div>

                <!-- Full Article container (will be populated when Open Article Here button is clicked) -->
                <div class="full-article-container mt-3" style="display: none;"></div>

                <!-- Notes container (will be populated when Notes button is clicked) -->
                <div class="notes-container mt-3" style="display: none;"></div>
            </div>
        </div>
    </div>
</template>

<script>
// Debounce function to prevent excessive API calls
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// Cache for already fetched topics
const topicCache = {};

// Utility functions for consistent UI interactions

// Function to show messages consistently
function showMessage(container, message, type = 'info', icon = null) {
    // Determine icon based on message type if not provided
    if (!icon) {
        switch(type) {
            case 'error':
                icon = 'bi-exclamation-triangle-fill text-danger';
                break;
            case 'success':
                icon = 'bi-check-circle-fill text-success';
                break;
            case 'warning':
                icon = 'bi-exclamation-circle-fill text-warning';
                break;
            case 'info':
            default:
                icon = 'bi-info-circle-fill text-info';
        }
    }

    // Create message HTML
    const messageHTML = `
        <div class="alert alert-${type === 'error' ? 'danger' : type} d-flex align-items-center" role="alert">
            <i class="bi ${icon} me-2"></i>
            <div>${message}</div>
        </div>
    `;

    // Clear existing content and show message
    container.innerHTML = messageHTML;
}

// Short helper functions
function showErrorMessage(container, message) {
    showMessage(container, message, 'error');
}

function showSuccessMessage(container, message) {
    showMessage(container, message, 'success');
}

function showInfoMessage(container, message) {
    showMessage(container, message, 'info');
}

function showWarningMessage(container, message) {
    showMessage(container, message, 'warning');
}

function showLoadingSpinner(container, message = 'Loading...') {
    container.innerHTML = `
        <div class="d-flex justify-content-center align-items-center my-4">
            <div class="spinner-border text-primary me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>${message}</div>
        </div>
    `;
}

// Function to toggle button active state
function toggleButtonActive(button, isActive) {
    if (isActive) {
        button.classList.add('active');
        button.setAttribute('aria-pressed', 'true');
    } else {
        button.classList.remove('active');
        button.setAttribute('aria-pressed', 'false');
    }
}

// Function to toggle visibility of container
function toggleContainerVisibility(container, isVisible) {
    if (isVisible) {
        container.style.display = 'block';
    } else {
        container.style.display = 'none';
    }
}

// Combine button and container toggle in one function
function toggleButtonAndContainer(button, containerSelector, isActive = null) {
    // Find the card and container
    const card = button.closest('.card');
    const container = typeof containerSelector === 'string'
        ? card.querySelector(containerSelector)
        : containerSelector;

    // If isActive is not provided, toggle based on current state
    if (isActive === null) {
        isActive = !button.classList.contains('active');
    }

    // Toggle button and container state
    if (isActive) {
        button.classList.add('active');
        button.setAttribute('aria-pressed', 'true');
        if (container) {
            container.style.display = 'block';
        }
    } else {
        button.classList.remove('active');
        button.setAttribute('aria-pressed', 'false');
        if (container) {
            container.style.display = 'none';
        }
    }

    return isActive;
}

// Function to get background context with WebSocket support
function pollForBackgroundContext(title, topic, container, url = '') {
    console.log("Setting up WebSocket for context updates...", {title, topic, url});

    // Get API content if available from the card
    const card = container.closest('.card');
    const apiContent = card ? (card.dataset.apiContent || '') : '';

    // Show a loading indicator with "Generating Additional Context..." message
    container.innerHTML = `
        <div class="d-flex justify-content-center my-4">
            <div class="spinner-border text-primary me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Generating Additional Context...</div>
        </div>
    `;

    // Ensure container is visible immediately
    container.style.display = 'block';
    container.style.visibility = 'visible';
    container.style.opacity = '1';

    // Create a standardized cache key that matches what the server will use
    // This avoids needing to get the key from a separate request
    const cache_key = `background_${title}_${topic}`;
    console.log("Using cache key:", cache_key);

    // Set up WebSocket connection directly
    setupWebSocketForContext(cache_key, container);
}

// Function to setup WebSocket connection for context updates
function setupWebSocketForContext(cache_key, container) {
    // Connect to the WebSocket server
    const socket = io('/context');

    // Track if we've received content
    let contentReceived = false;

    // Set up event listeners
    socket.on('connect', () => {
        console.log('WebSocket connected for context updates');

        // Subscribe to updates for this specific cache key
        socket.emit('subscribe', { cache_key: cache_key });
    });

    socket.on('disconnect', () => {
        console.log('WebSocket disconnected');
        if (!contentReceived) {
            showWarningMessage(container, 'Connection lost. Try refreshing the page.');
        }
    });

    // Handle context ready event (when content is available)
    socket.on('context_ready', (data) => {
        if (data.cache_key === cache_key && !contentReceived) {
            contentReceived = true;
            console.log('Received context via WebSocket:', {
                from_cache: data.from_cache,
                generation_time: data.generation_time
            });

            // Display the content
            displayEnrichedContent(container, data.enriched_content);

            // Disconnect after receiving content
            socket.disconnect();
        }
    });

    // Handle progress updates
    socket.on('context_update', (data) => {
        if (data.cache_key === cache_key) {
            console.log('Context generation progress:', data);
            updateContextProgress(container, data);
        }
    });

    // Handle errors
    socket.on('context_error', (data) => {
        if (data.cache_key === cache_key && !contentReceived) {
            contentReceived = true;
            console.error('Error during context generation:', data.message);

            if (data.content) {
                // Display the error content provided by the server
                displayEnrichedContent(container, data.content);
            } else {
                showErrorMessage(container, "An error occurred while generating background context.");
            }

            // Disconnect after receiving error
            socket.disconnect();
        }
    });

    // Set a timeout for the WebSocket connection (fallback)
    setTimeout(() => {
        if (!contentReceived) {
            showWarningMessage(container, 'Context generation is taking longer than expected. It will appear when ready.');
        }
    }, 45000); // 45 second timeout
}

// Helper function to update progress indicator
function updateContextProgress(container, data) {
    // Replace progress bar with simple message
    container.innerHTML = `
        <div class="text-center my-4">
            <div class="spinner-border text-primary mb-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Generating Additional Context...</div>
        </div>
    `;
}

// Helper function to display the enriched content
function displayEnrichedContent(container, content) {
    try {
        // Process the received content
        if (content.includes('class="enriched-context"')) {
            // The content already has the enriched-context wrapper
            container.innerHTML = content;

            // Check if it has the title, if not add it
            const enrichedContext = container.querySelector('.enriched-context');
            if (enrichedContext && !enrichedContext.querySelector('h5')) {
                // Add the title at the beginning
                const titleElement = document.createElement('h5');
                titleElement.className = 'mb-3';
                titleElement.style.fontFamily = 'var(--font-franklin)';
                titleElement.innerHTML = '<i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context';
                enrichedContext.insertBefore(titleElement, enrichedContext.firstChild);
            }
        } else {
            // Wrap the content with the enriched-context div including the title
            container.innerHTML = `
                <div class="enriched-context">
                    <h5 class="mb-3" style="font-family: var(--font-franklin);"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
                    ${content}
                </div>
            `;
        }

        // Debug - check what was inserted into the DOM
        console.log("Container after setting content:", container.innerHTML.substring(0, 100) + "...");
        console.log("Container has enriched-context element:", !!container.querySelector('.enriched-context'));

        // Force container to be visible
        container.style.display = 'block';
        container.style.visibility = 'visible';
        container.style.opacity = '1';

        // Add close button
        addCloseButton(container);
    } catch (error) {
        console.error("Error displaying enriched content:", error);
        showErrorMessage(container, "Error displaying content. Please try again.");
    }
}

// Helper function to add close button
function addCloseButton(container) {
    // Add close button directly to the enriched-context div
    const enrichedContext = container.querySelector('.enriched-context');
    if (enrichedContext) {
        // Check if there's already a close button
        if (!enrichedContext.querySelector('.close-btn')) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'close-btn';
            closeBtn.innerHTML = 'Close';
            closeBtn.setAttribute('aria-label', 'Close');

            // Add event listener to close button
            closeBtn.addEventListener('click', function() {
                // Hide the context container
                container.style.display = 'none';
                // Deactivate the button
                const contextBtn = container.closest('.card').querySelector('.context-btn');
                if (contextBtn) {
                    contextBtn.classList.remove('active');
                    contextBtn.setAttribute('aria-pressed', 'false');

                    // Scroll the card into view
                    setTimeout(() => {
                        const card = container.closest(".card");
                        card.scrollIntoView({ behavior: "smooth", block: "center" });
                    }, 50);
                }
            });

            // Append the button
            enrichedContext.appendChild(closeBtn);
        }
    }
}

// Function to load articles for a topic
function loadArticlesForTopic(topic, sourceType = null) {
    let currentTopic = topic;
    const container = document.getElementById('articles-container');

    // Debug logging
    console.log(`loadArticlesForTopic called with topic: ${topic}, sourceType: ${sourceType}`);

    // Hide welcome, show content section
    document.getElementById('welcome-section').style.display = 'none';
    document.getElementById('content-section').style.display = 'block';

    // Determine source type if not explicitly provided
    if (!sourceType) {
        // Check if the clicked button has newsletter-btn class
        const clickedButton = event && event.target ? event.target : null;
        console.log('No sourceType provided, checking clicked button:', clickedButton);
        if (clickedButton && clickedButton.classList.contains('newsletter-btn')) {
            sourceType = 'newsletter';
            console.log('Detected newsletter button');
        } else {
            sourceType = 'news';
            console.log('Detected news button or default to news');
        }
    }

    console.log(`Final sourceType: ${sourceType}`);

    // Update active state - only activate the clicked button and deactivate all others
    document.querySelectorAll('.topic-btn, .modal-topic-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Activate only the clicked button
    const clickedButton = event && event.target ? event.target : null;
    if (clickedButton && (clickedButton.classList.contains('topic-btn') || clickedButton.classList.contains('modal-topic-btn'))) {
        clickedButton.classList.add('active');
        console.log('Activated clicked button:', clickedButton);
    } else {
        // If no clicked button found, activate the first button with matching topic and source type
        const targetClass = sourceType === 'newsletter' ? 'newsletter-btn' : 'topic-btn';
        const targetButton = document.querySelector(`.${targetClass}[data-topic="${topic}"], .modal-topic-btn.${targetClass}[data-topic="${topic}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
            console.log('Activated target button:', targetButton);
        }
    }

    // Show loading spinner - simplified to avoid duplicate elements
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status"></div>
            <h5 class="mt-3">Loading...</h5>
            <p class="text-muted small">Please wait while we fetch the latest news</p>
        </div>
    `;

    // Set a minimum loading time to prevent flickering
    const minLoadingTime = 1500; // 1.5 second minimum loading time
    const loadingStartTime = Date.now();

    // Add retry mechanism with exponential backoff
    const fetchArticles = (attempt = 1, maxAttempts = 5) => {
        console.log(`Fetching articles for ${topic} (${sourceType}), attempt ${attempt} of ${maxAttempts}`);

        // Determine the endpoint and parameters based on topic
        let fetchUrl;
        if (topic === 'YouTube Channels') {
            // For YouTube Channels, fetch from /youtube endpoint with ByteByteGo channel
            fetchUrl = `/youtube?channel=bytebytego`;
        } else {
            // For regular topics, fetch from /news endpoint
            fetchUrl = `/news?topic=${encodeURIComponent(topic)}&source_type=${sourceType}`;
        }

        // Fetch articles for this topic with filter parameter and source type
        fetch(fetchUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(articles => {
                // Calculate how long we've been loading
                const loadingTime = Date.now() - loadingStartTime;
                const remainingTime = Math.max(0, minLoadingTime - loadingTime);

                // Wait for the minimum loading time before displaying content
                setTimeout(() => {
                    if (articles.length === 0) {
                        if (attempt < maxAttempts) {
                            // Try again with backoff - more gradual for 5 attempts
                            const backoffDelay = 1000 * (attempt * 1.5); // More gradual backoff: 1.5s, 3s, 4.5s, 6s...
                            console.log(`No articles found on attempt ${attempt}/${maxAttempts}, retrying in ${backoffDelay}ms...`);

                            // Update the loading message to show retry attempt
                            container.innerHTML = `
                                <div class="text-center py-5">
                                    <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status"></div>
                                    <h5 class="mt-3">Loading (attempt ${attempt+1}/${maxAttempts})...</h5>
                                    <p class="text-muted small">Still searching for the latest news on this topic</p>
                                </div>
                            `;

                            setTimeout(() => fetchArticles(attempt + 1, maxAttempts), backoffDelay);
                            return;
                        } else {
                            showInfoMessage(container, 'No articles found for this topic. Please try another topic.');
                            return;
                        }
                    }

                    // Articles are already sorted by the backend (newest first)
                    // Create article cards
                    createArticleCards(articles, topic);

                    // Article count display removed - no longer showing count to users
                }, remainingTime);
            })
            .catch(error => {
                // If we have more attempts, try again with backoff
                if (attempt < maxAttempts) {
                    const backoffDelay = 1000 * (attempt * 1.5); // More gradual backoff: 1.5s, 3s, 4.5s, 6s...
                    console.log(`Error on attempt ${attempt}/${maxAttempts}, retrying in ${backoffDelay}ms:`, error);

                    // Update the loading message to show retry attempt
                    container.innerHTML = `
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status"></div>
                            <h5 class="mt-3">Loading (attempt ${attempt+1}/${maxAttempts})...</h5>
                            <p class="text-muted small">Connection issue detected, retrying...</p>
                        </div>
                    `;

                    setTimeout(() => fetchArticles(attempt + 1, maxAttempts), backoffDelay);
                    return;
                }

                // Improved error handling with retry button after all attempts fail
                console.error('Error loading articles:', error);
                const errorMessage = `
                    <div class="alert alert-warning text-center py-4">
                        <i class="bi bi-exclamation-triangle-fill text-warning mb-3" style="font-size: 2rem;"></i>
                        <h5>Unable to load articles</h5>
                        <p>${error.message || 'An error occurred while loading the articles.'}</p>
                        <button class="btn btn-primary mt-2 retry-button">
                            <i class="bi bi-arrow-repeat me-1"></i> Try Again
                        </button>
                    </div>
                `;
                container.innerHTML = errorMessage;

                // Add event listener to retry button
                container.querySelector('.retry-button').addEventListener('click', () => {
                    loadArticlesForTopic(topic, sourceType);
                });
            });
    };

    // Start the first fetch attempt
    fetchArticles();
}

// Function to create article cards from template
function createArticleCards(articles, topic) {
    const container = document.getElementById('articles-container');
    const template = document.getElementById('article-template');
    container.innerHTML = '';

    // Function to set data attributes efficiently
    const setElementData = (element, dataObj) => {
        for (const [key, value] of Object.entries(dataObj)) {
            element.dataset[key] = value || '';
        }
    };

    articles.forEach(article => {
        // Clone template
        const articleCard = document.importNode(template.content, true);
        const card = articleCard.querySelector('.card');

        // Set article details
        articleCard.querySelector('.source-badge').textContent = article.source;
        articleCard.querySelector('.date').textContent = article.published_at;
        articleCard.querySelector('.card-title').textContent = article.title;

        // Store image URL and article URL as data attributes on the card
        card.dataset.imageUrl = article.image_url || '';
        card.dataset.url = article.url || '';

        // Add the same badge class to all sources
        const sourceBadge = articleCard.querySelector('.source-badge');
        sourceBadge.classList.remove('bg-primary');
        sourceBadge.classList.add('news-source-badge');

        // Set source name to uppercase
        sourceBadge.textContent = sourceBadge.textContent.toUpperCase();

        // Set description if available - hide for YouTube Channels
        if (topic === 'YouTube Channels') {
            // Hide the short description for YouTube content
            articleCard.querySelector('.short-description').style.display = 'none';
        } else if (article.short_description) {
            articleCard.querySelector('.short-description').textContent = article.short_description;
        }

        // Add image if available
        const imageContainer = articleCard.querySelector('.article-image-container');
        if (article.image_url) {
            imageContainer.innerHTML = `
                <div class="article-img-wrapper">
                    <img src="${article.image_url}" class="card-img-top article-img" alt="${article.title}" onerror="this.style.display='none'">
                </div>
            `;
        } else {
            // If no image, hide the container to save space
            imageContainer.style.display = 'none';
        }

        // Set article URL for "Read Article" button
        const readMoreBtn = articleCard.querySelector('.read-more-btn');

        // Set data attributes on buttons
        const contextBtn = articleCard.querySelector('.context-btn');
        const askAnythingBtn = articleCard.querySelector('.ask-anything-btn');
        const summarizeBtn = articleCard.querySelector('.summarize-btn');
        const openArticleHereBtn = articleCard.querySelector('.open-article-here-btn');
        const notesBtn = articleCard.querySelector('.notes-btn');

        // Set container data for each button
        summarizeBtn.dataset.container = '.summary-section';
        contextBtn.dataset.container = '.context-container';
        askAnythingBtn.dataset.container = '.ask-anything-container';
        openArticleHereBtn.dataset.container = '.full-article-container';
        notesBtn.dataset.container = '.notes-container';

        // Set data attributes efficiently
        const buttonData = {
            title: article.title,
            topic: article.topic || '',
            url: article.url // Add URL to buttonData
        };

        setElementData(contextBtn, buttonData);
        setElementData(askAnythingBtn, buttonData);
        setElementData(readMoreBtn, buttonData); // Set URL data attribute on Read button too
        setElementData(openArticleHereBtn, buttonData); // Set URL data attribute on Open Article Here button
        setElementData(notesBtn, buttonData); // Set URL data attribute on Notes button

        // Change button text for YouTube Channel topics
        if (topic === 'YouTube Channels') {
            readMoreBtn.innerHTML = '<i class="bi bi-box-arrow-up-right"></i> Watch Video';
            openArticleHereBtn.innerHTML = '<i class="bi bi-book"></i> <span>Open Video Here</span>';
        }

        // Add the card to the container
        container.appendChild(articleCard);
    });

    // Add event listeners to buttons
    addSummarizeButtonListeners();
    addContextButtonListeners();
    addAskAnythingListeners();
    addReadArticleButtonListeners(); // Add Read Article button listeners
    addOpenArticleHereButtonListeners(); // Add Open Article Here button listeners
    addNotesButtonListeners(); // Add Notes button listeners
}

// Function to handle the Summarize button clicks
function addSummarizeButtonListeners() {
    document.querySelectorAll('.summarize-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const card = this.closest('.card');
            const summarySection = card.querySelector('.summary-section');

            // Toggle button - modified to allow multiple active features
            const isActive = !this.classList.contains('active');
            if (isActive) {
                this.classList.add('active');
                this.setAttribute('aria-pressed', 'true');
                summarySection.style.display = 'block';
            } else {
                this.classList.remove('active');
                this.setAttribute('aria-pressed', 'false');
                summarySection.style.display = 'none';
                return; // Exit if we're deactivating
            }

            // Get the article URL from data attribute instead of href
            const url = card.querySelector('.read-more-btn').dataset.url;
            const title = card.querySelector('.card-title').textContent;
            const description = card.querySelector('.short-description').textContent || '';

            // Get image URL
            const imageUrl = card.dataset.imageUrl || '';

            // Show loading spinner
            showLoadingSpinner(summarySection, 'Generating AI summary...');

            // Fetch the summary from the server - include image if not already in the card
            const includeImage = !card.querySelector('.article-image-container img');

            // Create request payload
            const requestData = {
                url: url,
                title: title,
                include_image: includeImage,
                description: description,
                image_url: imageUrl
            };

            // Use POST instead of GET to handle larger content
            fetch('/summarize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Summary response:", data); // Debug log

                    if (data.error) {
                        showWarningMessage(summarySection, data.error);
                        return;
                    }

                    // Check if summary is being generated asynchronously
                    if (data.summary_pending) {
                        console.log("Summary is being generated asynchronously, setting up WebSocket...");
                        setupWebSocketForSummary(data.cache_key, summarySection, includeImage);
                        return;
                    }

                    // Handle immediate summary response (fallback)
                    displaySummaryContent(summarySection, data, includeImage);
                })
                .catch(error => {
                    console.error('Summary error:', error); // Log the error details
                    showErrorMessage(summarySection, 'Failed to fetch summary. Please try again later.');
                });
        });
    });
}

// Function to setup WebSocket connection for summary updates
function setupWebSocketForSummary(cache_key, summarySection, includeImage) {
    // Connect to the WebSocket server
    const socket = io('/summary');

    // Track if we've received content
    let contentReceived = false;

    // Set up event listeners
    socket.on('connect', () => {
        console.log('WebSocket connected for summary updates');

        // Subscribe to updates for this specific cache key
        socket.emit('subscribe', { cache_key: cache_key });
    });

    socket.on('disconnect', () => {
        console.log('WebSocket disconnected');
        if (!contentReceived) {
            showWarningMessage(summarySection, 'Connection lost. Try refreshing the page.');
        }
    });

    // Handle summary updates
    socket.on('summary_update', (data) => {
        if (data.cache_key === cache_key) {
            console.log('Summary generation progress:', data);

            if (data.status === 'started') {
                showLoadingSpinner(summarySection, 'Starting AI summary generation...');
            } else if (data.status === 'progress') {
                const progressText = data.message || 'Generating summary...';
                const progress = data.progress || 0;
                showLoadingSpinner(summarySection, `${progressText} (${progress}%)`);
            } else if (data.status === 'completed' && data.summary_data) {
                contentReceived = true;
                console.log('Summary completed via WebSocket');

                // Display the summary
                displaySummaryContent(summarySection, data.summary_data, includeImage);

                // Disconnect after receiving content
                socket.disconnect();
            } else if (data.status === 'error') {
                contentReceived = true;
                console.error('Error during summary generation:', data.message);

                if (data.summary_data) {
                    // Display the error summary provided by the server
                    displaySummaryContent(summarySection, data.summary_data, includeImage);
                } else {
                    showErrorMessage(summarySection, "An error occurred while generating the summary.");
                }

                // Disconnect after receiving error
                socket.disconnect();
            }
        }
    });

    // Set a timeout for the WebSocket connection (fallback)
    setTimeout(() => {
        if (!contentReceived) {
            showWarningMessage(summarySection, 'Summary generation is taking longer than expected. It will appear when ready.');
        }
    }, 60000); // 60 second timeout
}

// Function to display summary content
function displaySummaryContent(summarySection, data, includeImage) {
    // Build HTML for summary
    let summaryHTML = '<div class="summary-content">';

    // Add image if available from the summary and not already shown in the card
    if (data.image_url && includeImage) {
        summaryHTML += `
            <div class="summary-image mb-3">
                <img src="${data.image_url}" class="img-fluid rounded" alt="Article Image"
                    style="max-height: 280px; width: auto; display: block; margin: 0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
                    onerror="this.style.display='none'">
            </div>
        `;
    }

    // Add summary text first (swapped order)
    summaryHTML += `
        <div class="summary mb-3">
            <h6>Summary</h6>
            <p class="summary-text">${data.article_summary || 'No summary available.'}</p>
        </div>
    `;

    // Add key points after the summary
    summaryHTML += `
        <div class="key-points mb-3">
            <h6>Key Points</h6>
            <ul class="key-points-list">
                ${data.key_points && data.key_points.length > 0 ?
                  data.key_points.map(point => `<li>${point}</li>`).join('') :
                  '<li>No key points available.</li>'}
            </ul>
        </div>
    `;

    // Add close button
    summaryHTML += `
        <button class="close-btn">Close</button>
    `;

    summaryHTML += '</div>';

    // Set the HTML content of the summary section
    summarySection.innerHTML = summaryHTML;

    // Add event listener to the close button
    const closeBtn = summarySection.querySelector('.close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            summarySection.style.display = 'none';
            const summarizeBtn = summarySection.closest('.card').querySelector('.summarize-btn');
            if (summarizeBtn) {
                summarizeBtn.classList.remove('active');
                summarizeBtn.setAttribute('aria-pressed', 'false');
            }

            // Use setTimeout to ensure DOM updates before scrolling
            setTimeout(() => {
                // Scroll the card into view
                const card = summarySection.closest(".card");
                if (card) {
                    card.scrollIntoView({ behavior: "smooth", block: "center" });
                }
            }, 50); // 50ms delay
        });
    }
}

function addContextButtonListeners() {
    document.querySelectorAll('.context-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const card = this.closest('.card');
            const contextContainer = card.querySelector('.context-container');

            // Toggle button - modified to allow multiple active features
            const isActive = !this.classList.contains('active');
            if (isActive) {
                this.classList.add('active');
                this.setAttribute('aria-pressed', 'true');
                contextContainer.style.display = 'block';
            } else {
                this.classList.remove('active');
                this.setAttribute('aria-pressed', 'false');
                contextContainer.style.display = 'none';
                return; // Exit if we're deactivating
            }

            // Show loading indicator with "Generating Additional Context..." message without the outline box
            contextContainer.innerHTML = `
                <div class="d-flex justify-content-center my-4">
                    <div class="spinner-border text-primary me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Generating Additional Context...</div>
                </div>
            `;

            // Ensure container is visible immediately
            contextContainer.style.display = 'block';
            contextContainer.style.visibility = 'visible';
            contextContainer.style.opacity = '1';

            // Get necessary data
            const title = this.dataset.title;
            const topic = this.dataset.topic || '';
            const shortDescription = card.querySelector('.card-text')?.textContent || '';
            // FIXED: Get URL directly from this button's data attribute instead of read-more-btn
            const url = this.dataset.url || '';

            // Add debug logging for URL consistency
            console.log(`Context request for article: "${title}"`);
            console.log(`Using URL from context button data attribute: ${url}`);

            // Create a default summary JSON
            const defaultSummary = JSON.stringify({
                "summary": shortDescription,
                "key_points": []
            });

            // No need to get API content - backend will handle it

            // Fetch context
            fetch('/background', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: title,
                    topic: topic,
                    summary: defaultSummary,
                    url: url  // Send the URL to the backend
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.context_pending) {
                    // Context is being generated asynchronously
                    // Setup WebSocket directly instead of polling with another request
                    console.log("Setting up WebSocket for context updates with key:", data.cache_key);
                    setupWebSocketForContext(data.cache_key, contextContainer);
                } else if (data.enriched_content) {
                    // Context is immediately available
                    console.log("Context available immediately:", data.enriched_content.substring(0, 100) + "...");

                    // Check if content is empty or invalid
                    if (!data.enriched_content.trim()) {
                        console.error("Received empty enriched content in immediate context");
                        showErrorMessage(contextContainer, "Error: Received empty content from server.");
                        return;
                    }

                    try {
                        // Set the content
                        contextContainer.innerHTML = data.enriched_content;

                        // Debug - check what was inserted into the DOM
                        console.log("Container after setting immediate content:", contextContainer.innerHTML.substring(0, 100) + "...");
                        console.log("Container has enriched-context element:", !!contextContainer.querySelector('.enriched-context'));

                        // Ensure container is visible
                        contextContainer.style.display = 'block';
                        contextContainer.style.visibility = 'visible';
                        contextContainer.style.opacity = '1';

                        // Force layout recalculation
                        console.log("Container display style:", contextContainer.style.display, "- Visibility:", window.getComputedStyle(contextContainer).visibility);

                        // Add close button to the context if it has the enriched-context class
                        const enrichedContext = contextContainer.querySelector('.enriched-context');
                        console.log("Found enriched context element:", !!enrichedContext);
                        if (enrichedContext) {
                            // Check if there's already a close button
                            if (!enrichedContext.querySelector('.close-btn')) {
                                const closeBtn = document.createElement('button');
                                closeBtn.className = 'close-btn';
                                closeBtn.innerHTML = 'Close';
                                closeBtn.setAttribute('aria-label', 'Close');

                                // Add event listener to close button
                                closeBtn.addEventListener('click', function() {
                                    // Hide the context container
                                    contextContainer.style.display = 'none';
                                    // Deactivate the button
                                    const contextBtn = contextContainer.closest('.card').querySelector('.context-btn');
                                    if (contextBtn) {
                                        contextBtn.classList.remove('active');
                                        contextBtn.setAttribute('aria-pressed', 'false');

                                        // Use setTimeout to ensure DOM updates before scrolling
                                        setTimeout(() => {
                                            // Scroll the card into view
                                            const card = contextContainer.closest(".card");
                                            card.scrollIntoView({ behavior: "smooth", block: "center" });
                                        }, 50); // 50ms delay
                                    }
                                });

                                // Make sure the button is appended at the end, after any existing content
                                enrichedContext.appendChild(closeBtn);
                                console.log("Added close button to enriched context");
                            }
                        } else {
                            console.error("No enriched-context element found in immediate response HTML");
                            // Try to add the enriched-context wrapper if it doesn't exist
                            const wrapper = document.createElement('div');
                            wrapper.className = 'enriched-context';

                            // Add title and the original content
                            wrapper.innerHTML = `
                                <h5 class="mb-3" style="font-family: var(--font-franklin);"><i class="bi bi-lightbulb-fill text-warning me-2"></i>Background & Context</h5>
                                ${contextContainer.innerHTML}
                            `;
                            contextContainer.innerHTML = '';
                            contextContainer.appendChild(wrapper);

                            // Now add the close button
                            const closeBtn = document.createElement('button');
                            closeBtn.className = 'close-btn';
                            closeBtn.innerHTML = 'Close';
                            closeBtn.setAttribute('aria-label', 'Close');

                            closeBtn.addEventListener('click', function() {
                                contextContainer.style.display = 'none';
                                const contextBtn = contextContainer.closest('.card').querySelector('.context-btn');
                                if (contextBtn) {
                                    contextBtn.classList.remove('active');
                                    contextBtn.setAttribute('aria-pressed', 'false');

                                    // Scroll the card into view
                                    const card = contextContainer.closest(".card");
                                    card.scrollIntoView({ behavior: "smooth", block: "center" });
                                }
                            });

                            wrapper.appendChild(closeBtn);
                            console.log("Created enriched-context wrapper and added close button for immediate context");
                        }

                        // Force a repaint by temporarily modifying a style property
                        contextContainer.style.transform = 'translateZ(0)';
                        setTimeout(() => {
                            contextContainer.style.transform = '';

                            // Scroll to the context container with a small delay to ensure it's rendered
                            setTimeout(() => {
                                const enrichedContext = contextContainer.querySelector('.enriched-context');
                                if (enrichedContext) {
                                    enrichedContext.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                                }
                            }, 50);
                        }, 100);
                    } catch (error) {
                        console.error("Error displaying immediate context:", error);
                        showErrorMessage(contextContainer, "Error displaying additional context. Please try again.");
                    }
                } else if (data.error) {
                    showWarningMessage(contextContainer, data.error);
                }
            })
            .catch(error => {
                showErrorMessage(contextContainer, error, 'Error loading Additional Context. Please try again.');
            });
        });
    });
}

function addAskAnythingListeners() {
    // Create a cache for AI responses
    const responseCache = {};

    document.querySelectorAll('.ask-anything-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const card = this.closest('.card');
            const askContainer = card.querySelector('.ask-anything-container');

            // Toggle button - modified to allow multiple active features
            const isActive = !this.classList.contains('active');
            if (isActive) {
                this.classList.add('active');
                this.setAttribute('aria-pressed', 'true');
                askContainer.style.display = 'block';
            } else {
                this.classList.remove('active');
                this.setAttribute('aria-pressed', 'false');
                askContainer.style.display = 'none';
                return; // Exit if we're deactivating
            }

            const title = this.dataset.title;
            const topic = this.dataset.topic || '';
            const url = this.dataset.url || '';  // Get URL from data attribute

            // Add question form
            askContainer.innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <form class="ask-anything-form">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Ask birdii" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                        </form>
                        <div class="ask-anything-results mt-3"></div>
                    </div>
                </div>
            `;

            // Store the URL in a variable that will be accessible to the form submit handler
            const buttonUrl = url;  // Save the URL from the button's dataset

            // Add submit handler for the form
            const form = askContainer.querySelector('.ask-anything-form');
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                const question = this.querySelector('input').value.trim();
                const resultsContainer = askContainer.querySelector('.ask-anything-results');

                if (!question) return;

                // Create a cache key from the title and question
                const cacheKey = `${title}:${question}`;

                // Check if we already have this response cached
                if (responseCache[cacheKey]) {
                    resultsContainer.innerHTML = responseCache[cacheKey];

                    // Re-add event listener to the close button (since innerHTML doesn't preserve event listeners)
                    const closeBtn = resultsContainer.querySelector('.close-btn');
                    if (closeBtn) {
                        closeBtn.addEventListener('click', () => {
                            // Hide results container
                            resultsContainer.style.display = 'none';

                            // Use setTimeout to ensure DOM updates before scrolling
                            setTimeout(() => {
                                // Scroll the card into view
                                const card = resultsContainer.closest(".card");
                                card.scrollIntoView({ behavior: "smooth", block: "center" });
                            }, 50); // 50ms delay

                            // Clear results after scrolling (optional)
                            setTimeout(() => {
                                resultsContainer.innerHTML = '';
                                resultsContainer.style.display = 'block';
                            }, 300);
                        });
                    }
                    return;
                }

                // Show loading spinner
                showLoadingSpinner(resultsContainer, 'Thinking about your question...');

                try {
                    // IMPORTANT: Use the URL from the button's data attribute instead of the card
                    // This ensures we're using the correct URL that was set when creating the card
                    console.log(`Ask Anything - Using URL from button: ${buttonUrl}`);

                    // Send the question to the server
                    const response = await fetch('/ask_anything', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            title: title,
                            question: question,
                            topic: topic,
                            url: buttonUrl  // Use the URL from the button's dataset
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`Server returned ${response.status}`);
                    }

                    const data = await response.json();
                    console.log("Ask anything response:", data); // Debug

                    if (data.error) {
                        showErrorMessage(resultsContainer, data.error);
                        return;
                    }

                    // Display the answer with a close button
                    resultsContainer.innerHTML = `
                        <div class="answer-content">
                            ${data.answer}
                        </div>
                        <button class="close-btn mt-3">Close</button>
                    `;

                    // Add event listener to the close button
                    resultsContainer.querySelector('.close-btn').addEventListener('click', () => {
                        // Hide results container
                        resultsContainer.style.display = 'none';

                        // Use setTimeout to ensure DOM updates before scrolling
                        setTimeout(() => {
                            // Scroll the card into view
                            const card = resultsContainer.closest(".card");
                            card.scrollIntoView({ behavior: "smooth", block: "center" });
                        }, 50); // 50ms delay

                        // Clear results after scrolling (optional)
                        setTimeout(() => {
                            resultsContainer.innerHTML = '';
                            resultsContainer.style.display = 'block';
                        }, 300);
                    });

                    // Cache the response (with close button HTML)
                    responseCache[cacheKey] = resultsContainer.innerHTML;

                    // Clear the input field and focus it for the next question
                    this.querySelector('input').value = '';
                    this.querySelector('input').focus();
                } catch (error) {
                    console.error('Ask Anything error:', error);
                    showErrorMessage(resultsContainer, 'Error processing your question. Please try again.');
                }
            });
        });
    });
}

// Function to handle the Read Article button clicks
function addReadArticleButtonListeners() {
    // Add event listeners to the Read Article links
    document.querySelectorAll('.read-more-btn').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the URL from data attribute
            const url = this.dataset.url;

            // Validate URL before opening
            if (url && url.startsWith('http')) {
                // Open the article URL in a new tab
                window.open(url, '_blank');
            } else {
                console.error("Invalid URL for Read Article link:", url);
            }
        });
    });
}

// Function to handle Open Article Here button clicks
function addOpenArticleHereButtonListeners() {
    document.querySelectorAll('.open-article-here-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const card = this.closest('.card');
            const fullArticleContainer = card.querySelector('.full-article-container');

            // Toggle button - modified to allow multiple active features
            const isActive = !this.classList.contains('active');
            if (isActive) {
                this.classList.add('active');
                this.setAttribute('aria-pressed', 'true');
                fullArticleContainer.style.display = 'block';
            } else {
                this.classList.remove('active');
                this.setAttribute('aria-pressed', 'false');
                fullArticleContainer.style.display = 'none';
                return; // Exit if we're deactivating
            }

            // Get necessary data
            const url = this.dataset.url;
            const title = this.dataset.title;

            // Validate URL
            if (!url || !url.startsWith('http')) {
                showErrorMessage(fullArticleContainer, 'Invalid URL. Cannot display article.');
                return;
            }

            // Show loading message
            showLoadingSpinner(fullArticleContainer, 'Loading original article...');

            // Create the iframe container with responsive styling and close button
            const iframeHTML = `
                <div class="article-content p-0">
                    <div class="iframe-container">
                        <iframe
                            src="/proxy_article?url=${encodeURIComponent(url)}"
                            allowfullscreen
                            referrerpolicy="no-referrer"
                            sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-presentation"
                        ></iframe>
                        <div class="iframe-loading-overlay">
                            <div class="spinner-border text-primary" role="status"></div>
                            <div class="loading-message">
                                <div class="mb-2">Loading original article...</div>
                                <div class="text-muted small">This may take a moment depending on the source website.</div>
                                <div class="text-muted small mt-2">Some websites may prevent embedding due to their security policies.</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-end mt-3">
                        <button class="close-btn">Close</button>
                    </div>
                </div>
            `;

            // Set the content
            fullArticleContainer.innerHTML = iframeHTML;

            // Add timeout to check if iframe hasn't loaded within 15 seconds
            const iframe = fullArticleContainer.querySelector('iframe');
            const loadingOverlay = fullArticleContainer.querySelector('.iframe-loading-overlay');

            // Better error handling
            if (iframe && loadingOverlay) {
                // Frame load success handler
                iframe.addEventListener('load', function() {
                    console.log("Iframe loaded for URL:", url);

                    // Hide loading overlay with a small delay to ensure content is rendered
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 100);

                    // Check if the iframe loaded successfully or was blocked
                    try {
                        // This will fail if iframe is cross-origin blocked
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                        // Check if we got a blank page or an error page
                        if (iframeDoc.body.innerHTML.trim() === '' ||
                            iframeDoc.body.innerHTML.includes('Refused to connect') ||
                            iframeDoc.body.innerHTML.includes('X-Frame-Options')) {
                            throw new Error('Empty or error content');
                        }

                        console.log("Successfully accessed iframe content");
                    } catch (error) {
                        console.log('Iframe access blocked by browser security (likely X-Frame-Options):', error);

                        // Wait a moment to see if content appears despite the error
                        setTimeout(() => {
                            // Check if iframe appears empty (common with X-Frame-Options blocking)
                            const iframeRect = iframe.getBoundingClientRect();
                            if (iframeRect.width === 0 || iframeRect.height === 0 ||
                                (iframe.contentWindow && iframe.contentWindow.length === 0)) {

                                // Show error message with open in new tab button
                                loadingOverlay.style.display = 'flex';
                                loadingOverlay.innerHTML = `
                                    <div class="text-center py-4">
                                        <i class="bi bi-shield-lock text-warning mb-3" style="font-size: 2rem;"></i>
                                        <h5>Content cannot be displayed inline</h5>
                                        <p>This website prevents embedding in frames for security reasons.</p>
                                        <p class="mt-3"><a href="${url}" target="_blank" class="btn btn-sm btn-primary">Open in new tab instead</a></p>
                                    </div>
                                `;
                            }
                        }, 1000);
                    }
                });

                // Error handler for iframe loading failure
                iframe.addEventListener('error', function(e) {
                    console.error("Iframe error event:", e);
                    loadingOverlay.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-exclamation-triangle-fill text-warning mb-3" style="font-size: 2rem;"></i>
                            <h5>Unable to load article</h5>
                            <p>Our proxy service couldn't fetch the article content.</p>
                            <p class="mt-3"><a href="${url}" target="_blank" class="btn btn-sm btn-primary">Open in new tab instead</a></p>
                        </div>
                    `;
                });

                // Timeout for slow loading
                const loadTimeout = setTimeout(() => {
                    if (loadingOverlay.style.display !== 'none') {
                        // Try to detect if iframe has content despite loading overlay still showing
                        try {
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            if (iframeDoc && iframeDoc.body && iframeDoc.body.innerHTML.trim() !== '') {
                                // Content is there, just hide the overlay
                                loadingOverlay.style.display = 'none';
                                return;
                            }
                        } catch (e) {
                            // Cannot access iframe content - likely security restriction
                        }

                        // Show timeout message
                        loadingOverlay.innerHTML = `
                            <div class="text-center py-4">
                                <i class="bi bi-clock-history text-warning mb-3" style="font-size: 2rem;"></i>
                                <h5>Article loading is taking longer than expected</h5>
                                <p>Our proxy service may be having trouble fetching the content.</p>
                                <div class="mt-3 d-flex justify-content-center">
                                    <a href="${url}" target="_blank" class="btn btn-sm btn-primary me-2">
                                        <i class="bi bi-box-arrow-up-right"></i> Open in new tab
                                    </a>
                                    <button class="btn btn-sm btn-outline-secondary retry-btn">
                                        <i class="bi bi-arrow-clockwise"></i> Try again
                                    </button>
                                </div>
                            </div>
                        `;
                    }
                }, 15000); // 15 seconds timeout

                // Clear timeout when iframe successfully loads
                iframe.addEventListener('load', () => clearTimeout(loadTimeout));
            }

            // Add event listener to the close button
            fullArticleContainer.querySelector('.close-btn').addEventListener('click', () => {
                // Remove the iframe to stop any media/scripts
                const iframe = fullArticleContainer.querySelector('iframe');
                if (iframe) {
                    iframe.src = 'about:blank';
                }

                // Hide the container
                fullArticleContainer.style.display = 'none';
                this.classList.remove('active');
                this.setAttribute('aria-pressed', 'false');

                // Use setTimeout to ensure DOM updates before scrolling
                setTimeout(() => {
                    // Scroll the card into view
                    const card = fullArticleContainer.closest(".card");
                    card.scrollIntoView({ behavior: "smooth", block: "center" });
                }, 50); // 50ms delay
            });
        });
    });
}

// Function to handle Notes button clicks
function addNotesButtonListeners() {
    document.querySelectorAll('.notes-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const card = this.closest('.card');
            const notesContainer = card.querySelector('.notes-container');

            // Toggle button - modified to allow multiple active features
            const isActive = !this.classList.contains('active');
            if (isActive) {
                this.classList.add('active');
                this.setAttribute('aria-pressed', 'true');
                notesContainer.style.display = 'block';
            } else {
                this.classList.remove('active');
                this.setAttribute('aria-pressed', 'false');
                notesContainer.style.display = 'none';
                return; // Exit if we're deactivating
            }

            // Get necessary data
            const title = this.dataset.title;
            const articleUrl = this.dataset.url || '';

            // Create a unique ID for this article's notes
            const noteId = `notes_${btoa(articleUrl || title).replace(/=/g, '')}`;

            // Load existing notes
            const existingNotes = localStorage.getItem(noteId);

            // Create the notes form and display area
            notesContainer.innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <form class="notes-form">
                            <div class="mb-3">
                                <textarea class="form-control" rows="3" placeholder="Add your notes about this article...">${existingNotes || ''}</textarea>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-secondary save-notes-btn" type="button">
                                    <i class="bi bi-save"></i> Save
                                </button>
                                <button class="btn btn-outline-secondary close-notes-btn" type="button">
                                    Close
                                </button>
                            </div>
                        </form>
                        <div class="form-text text-muted mt-2">
                            Notes are saved locally in your browser.
                        </div>
                    </div>
                </div>
            `;

            // Add handlers for the buttons
            const saveButton = notesContainer.querySelector('.save-notes-btn');
            const closeButton = notesContainer.querySelector('.close-notes-btn');
            const textarea = notesContainer.querySelector('textarea');

            // Save notes to localStorage
            saveButton.addEventListener('click', function() {
                const notes = textarea.value.trim();

                // Save notes to localStorage
                if (notes) {
                    localStorage.setItem(noteId, notes);
                    // Show saved confirmation
                    const confirmText = document.createElement('div');
                    confirmText.className = 'text-success mt-2';
                    confirmText.innerHTML = '<i class="bi bi-check-circle"></i> Notes saved!';
                    this.parentNode.appendChild(confirmText);

                    // Remove confirmation after 2 seconds
                    setTimeout(() => {
                        confirmText.remove();
                    }, 2000);
                } else {
                    // If notes are empty, remove from storage
                    localStorage.removeItem(noteId);
                }
            });

            // Close notes panel
            closeButton.addEventListener('click', function() {
                notesContainer.style.display = 'none';
                btn.classList.remove('active');
                btn.setAttribute('aria-pressed', 'false');

                // Use setTimeout to ensure DOM updates before scrolling
                setTimeout(() => {
                    // Scroll the card into view
                    card.scrollIntoView({ behavior: "smooth", block: "center" });
                }, 50);
            });
        });
    });
}

// Update the listeners function to include all button features
function addEventListeners() {
    addSummarizeButtonListeners();
    addContextButtonListeners();
    addAskAnythingListeners();
    addReadArticleButtonListeners(); // Add Read Article button listeners
    addOpenArticleHereButtonListeners(); // Add Open Article Here button listeners
    addNotesButtonListeners(); // Add Notes button listeners
}

document.addEventListener('DOMContentLoaded', function() {
    // Helper function to properly encode HTML entities
    function encodeHTMLEntities(text) {
        const parser = new DOMParser();
        const dom = parser.parseFromString(
            `<!DOCTYPE html><body>${text}</body>`, 'text/html');
        return dom.body.textContent;
    }

    // Handle Browse Topics modal show/hide events
    const topicsModal = document.getElementById('topicsModal');
    const browseTopicsBtn = document.querySelector('.topics-modal-btn');

    if (topicsModal && browseTopicsBtn) {
        // Add active class when modal is shown
        topicsModal.addEventListener('show.bs.modal', function() {
            browseTopicsBtn.classList.add('active');
        });

        // Remove active class when modal is hidden
        topicsModal.addEventListener('hide.bs.modal', function() {
            browseTopicsBtn.classList.remove('active');
        });
    }

    // Dropdown functionality for main topic buttons
    document.querySelectorAll('.topic-dropdown').forEach(dropdown => {
        const mainBtn = dropdown.querySelector('.main-topic-btn');
        const dropdownContent = dropdown.querySelector('.topic-dropdown-content');

        // Toggle dropdown on main button click
        mainBtn.addEventListener('click', function(e) {
            e.stopPropagation();

            // Close all other dropdowns first
            document.querySelectorAll('.topic-dropdown-content').forEach(content => {
                if (content !== dropdownContent) {
                    content.classList.remove('show');
                }
            });
            document.querySelectorAll('.main-topic-btn').forEach(btn => {
                if (btn !== mainBtn) {
                    btn.classList.remove('active');
                }
            });

            // Toggle current dropdown
            dropdownContent.classList.toggle('show');
            mainBtn.classList.toggle('active');
        });

        // Handle topic selection
        dropdownContent.addEventListener('click', function(e) {
            if (e.target.classList.contains('topic-dropdown-item')) {
                e.stopPropagation();

                // Close dropdown
                dropdownContent.classList.remove('show');
                mainBtn.classList.remove('active');

                // Trigger the topic selection
                e.target.click();
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.topic-dropdown')) {
            document.querySelectorAll('.topic-dropdown-content').forEach(content => {
                content.classList.remove('show');
            });
            document.querySelectorAll('.main-topic-btn').forEach(btn => {
                btn.classList.remove('active');
            });
        }
    });

    // Apply HTML encoding to all topic buttons when they're displayed
    document.querySelectorAll('.welcome-topic-btn, .topic-btn, .modal-topic-btn').forEach(btn => {
        // Get the topic text but don't modify it again if it's already properly displayed
        const topic = btn.dataset.topic;

        // Add click listener
        btn.addEventListener('click', function(event) {
            // Determine source type based on button class
            const sourceType = this.classList.contains('newsletter-btn') ? 'newsletter' : 'news';
            loadArticlesForTopic(topic, sourceType);
        });
    });

    // Add event handlers for YouTube channel buttons
    document.querySelectorAll('.welcome-youtube-btn').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            // For YouTube channels, use 'YouTube Channels' as the topic
            loadArticlesForTopic('YouTube Channels', 'youtube');
        });
    });

    // If there's a default topic, load it
    if (document.querySelector('.welcome-topic-btn')) {
        const defaultTopic = document.querySelector('.welcome-topic-btn').dataset.topic;
        // Uncomment to auto-load the first topic
        // loadArticlesForTopic(defaultTopic);
    }
});

// Add retry button functionality
const retryBtn = loadingOverlay.querySelector('.retry-btn');
if (retryBtn) {
    retryBtn.addEventListener('click', function() {
        // Reset the iframe src to reload
        iframe.src = url;

        // Reset the loading overlay
        loadingOverlay.innerHTML = `
            <div class="spinner-border text-primary" role="status"></div>
            <div class="loading-message">
                <div class="mb-2">Retrying to load the article...</div>
                <div class="text-muted small">This may take a moment depending on the source website.</div>
            </div>
        `;
    });
}
</script>
{% endblock %}