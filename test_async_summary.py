#!/usr/bin/env python3
"""
Simple test script to verify the async summary implementation works.
"""
import requests
import json
import time

def test_async_summary():
    """Test the async summary endpoint."""
    
    # Test data
    test_data = {
        "url": "https://example.com/test-article",
        "title": "Test Article for Async Summary",
        "description": "This is a test article to verify async summary functionality.",
        "include_image": False,
        "force_refresh": True
    }
    
    print("Testing async summary implementation...")
    print(f"Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Make request to the summarize endpoint
        response = requests.post(
            "http://localhost:5000/summarize",
            json=test_data,
            timeout=10
        )
        
        print(f"\nResponse status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response body: {json.dumps(result, indent=2)}")
            
            # Check if it's async (should have summary_pending=True)
            if result.get('summary_pending'):
                print("\n✅ SUCCESS: Endpoint is now async!")
                print(f"Cache key: {result.get('cache_key')}")
                print("The summary will be generated in the background.")
                return True
            elif 'article_summary' in result:
                print("\n❌ ISSUE: Endpoint returned summary immediately (still synchronous)")
                return False
            else:
                print(f"\n❓ UNEXPECTED: Unexpected response format")
                return False
        else:
            print(f"\n❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("\n❌ ERROR: Could not connect to the server.")
        print("Make sure your Flask app is running on http://localhost:5000")
        return False
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_async_summary()
    if success:
        print("\n🎉 Async summary implementation is working correctly!")
    else:
        print("\n⚠️  There may be issues with the async implementation.")
